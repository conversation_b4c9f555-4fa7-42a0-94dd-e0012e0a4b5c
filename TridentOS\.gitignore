# TridentOS .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.production

# Logs
logs/
*.log
*.log.*

# Data files
data/
*.db
*.sqlite
*.sqlite3

# Backups
backups/
*.bak
*.backup

# Temporary files
tmp/
temp/
*.tmp
*.temp

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Kivy
*.kv~

# System files
.directory
desktop.ini

# Configuration overrides
config/local_*.yaml
config/local_*.yml
config/local_*.json
config/*.local.*

# Hardware calibration files
config/*_calibration.json
config/compass_cal.json

# SSL certificates and keys
*.pem
*.key
*.crt
*.cert

# Test coverage
.coverage
htmlcov/
.pytest_cache/

# Documentation builds
docs/_build/
docs/build/

# Assets (large files)
assets/videos/
assets/large_images/

# Development
.mypy_cache/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Raspberry Pi specific
*.img
*.iso

# Network configuration (sensitive)
config/network_secrets.yaml
config/wifi_passwords.yaml

# User data
user_data/
profiles/

# Runtime files
*.pid
*.sock
