# TridentOS - Struktura Projektu

## Kompletna struktura katalogów

```
TridentOS/
├── main.py                          # Główny plik startowy aplikacji
├── requirements.txt                 # Zależności Python
├── setup.sh                        # Skrypt instalacyjny dla Raspberry Pi 5
├── .env                            # <PERSON><PERSON>nne środowiskowe (konfiguracja)
├── .gitignore                      # Pliki ignorowane przez Git
├── README.md                       # Dokumentacja główna projektu
├── PROJECT_STRUCTURE.md            # Ten plik - struktura projektu
│
├── config/                         # Pliki konfiguracyjne systemu
│   ├── system.yaml                 # Konfiguracja główna systemu
│   ├── network.yaml                # Konfiguracja sieciowa
│   ├── hardware.yaml               # Konfiguracja sprzętowa (GPIO, UART, I2C)
│   ├── thresholds.yaml             # Progi alarmowe i ostrzeżenia
│   └── compass_cal.json            # Kalibracja kompasu (generowany)
│
├── src/                            # Kod źródłowy aplikacji
│   ├── __init__.py
│   │
│   ├── gui/                        # Interfejs <PERSON> (Kivy)
│   │   ├── __init__.py
│   │   ├── main_window.py          # Główne okno aplikacji
│   │   │
│   │   ├── screens/                # Ekrany aplikacji
│   │   │   ├── __init__.py
│   │   │   ├── dashboard.py        # Ekran główny - dashboard
│   │   │   ├── navigation.py       # Nawigacja i autopilot
│   │   │   ├── engine.py           # Kontrola silnika
│   │   │   ├── climate.py          # Klimatyzacja i wentylacja
│   │   │   ├── lighting.py         # Oświetlenie
│   │   │   ├── water.py            # Systemy wodne
│   │   │   ├── battery.py          # Baterie i zasilanie
│   │   │   ├── emergency.py        # Systemy awaryjne
│   │   │   └── settings.py         # Ustawienia systemu
│   │   │
│   │   └── widgets/                # Niestandardowe widżety GUI
│   │       ├── __init__.py
│   │       ├── status_card.py      # Karty statusu
│   │       ├── gauge.py            # Wskaźniki okrągłe
│   │       ├── switch.py           # Przełączniki
│   │       ├── slider.py           # Suwaki
│   │       └── chart.py            # Wykresy i grafy
│   │
│   ├── logic/                      # Logika biznesowa
│   │   ├── __init__.py
│   │   │
│   │   └── modules/                # Moduły logiczne systemu
│   │       ├── __init__.py
│   │       ├── autopilot.py        # Autopilot i nawigacja
│   │       ├── engine.py           # Kontrola silnika
│   │       ├── climate.py          # Klimatyzacja
│   │       ├── battery.py          # Zarządzanie bateriami
│   │       ├── fuel.py             # Zarządzanie paliwem
│   │       ├── emergency.py        # Systemy awaryjne
│   │       ├── lighting.py         # Kontrola oświetlenia
│   │       └── water.py            # Systemy wodne
│   │
│   ├── drivers/                    # Sterowniki sprzętowe
│   │   ├── __init__.py
│   │   ├── gpio_driver.py          # Sterownik GPIO Raspberry Pi
│   │   ├── uart_driver.py          # Komunikacja UART
│   │   ├── i2c_driver.py           # Komunikacja I2C
│   │   ├── spi_driver.py           # Komunikacja SPI
│   │   │
│   │   └── interfaces/             # Interfejsy sprzętowe
│   │       ├── __init__.py
│   │       ├── sensor_interface.py # Interfejs czujników
│   │       ├── relay_interface.py  # Interfejs przekaźników
│   │       └── display_interface.py# Interfejs wyświetlaczy
│   │
│   ├── services/                   # Usługi systemowe
│   │   ├── __init__.py
│   │   ├── data_service.py         # Centralna usługa danych
│   │   ├── system_monitor.py       # Monitor systemu
│   │   ├── network_service.py      # Usługi sieciowe
│   │   ├── web_service.py          # Serwer WWW
│   │   ├── api_service.py          # API REST
│   │   ├── websocket_service.py    # WebSocket dla real-time
│   │   └── backup_service.py       # Kopie zapasowe
│   │
│   └── utils/                      # Narzędzia pomocnicze
│       ├── __init__.py
│       ├── logger.py               # System logowania
│       ├── config_manager.py       # Zarządzanie konfiguracją
│       ├── error_handler.py        # Obsługa błędów
│       ├── helpers.py              # Funkcje pomocnicze
│       ├── validators.py           # Walidatory danych
│       └── constants.py            # Stałe systemowe
│
├── tests/                          # Testy jednostkowe
│   ├── __init__.py
│   ├── test_config_manager.py      # Testy managera konfiguracji
│   ├── test_data_service.py        # Testy usługi danych
│   ├── test_gpio_driver.py         # Testy sterownika GPIO
│   ├── test_autopilot.py           # Testy autopilota
│   ├── test_engine.py              # Testy modułu silnika
│   └── conftest.py                 # Konfiguracja pytest
│
├── docs/                           # Dokumentacja
│   ├── README.md                   # Przegląd dokumentacji
│   ├── installation.md             # Instrukcja instalacji
│   ├── user_manual.md              # Podręcznik użytkownika
│   ├── developer_guide.md          # Przewodnik developera
│   ├── api_reference.md            # Dokumentacja API
│   ├── hardware_setup.md           # Konfiguracja sprzętu
│   ├── troubleshooting.md          # Rozwiązywanie problemów
│   ├── configuration.md            # Konfiguracja systemu
│   └── faq.md                      # Często zadawane pytania
│
├── assets/                         # Zasoby statyczne
│   ├── images/                     # Obrazy i ikony
│   │   ├── logo.png
│   │   ├── backgrounds/
│   │   └── icons/
│   │
│   └── sounds/                     # Dźwięki i alarmy
│       ├── alarms/
│       ├── notifications/
│       └── system/
│
├── Icons/                          # Ikony (istniejące)
│   ├── bulb.png
│   ├── compass.png
│   ├── drop.png
│   ├── emergency.png
│   ├── engine.png
│   ├── file.png
│   ├── graph.png
│   └── setting.png
│
├── logs/                           # Logi systemowe
│   ├── tridentos.log              # Log główny (generowany)
│   ├── audit.log                  # Log audytu (generowany)
│   └── error.log                  # Log błędów (generowany)
│
├── data/                           # Dane aplikacji
│   ├── tridentos.db               # Baza danych SQLite (generowana)
│   ├── sensor_data/               # Dane z czujników
│   └── user_profiles/             # Profile użytkowników
│
└── backups/                       # Kopie zapasowe
    ├── config_backups/            # Kopie konfiguracji
    ├── data_backups/              # Kopie danych
    └── system_backups/            # Kopie systemu
```

## Opis głównych komponentów

### 1. **Główne pliki**
- `main.py` - Punkt wejścia aplikacji, inicjalizacja Kivy
- `requirements.txt` - Wszystkie zależności Python
- `setup.sh` - Automatyczna instalacja na Raspberry Pi 5
- `.env` - Konfiguracja środowiska (porty, hasła, klucze API)

### 2. **Konfiguracja (`config/`)**
- Pliki YAML z konfiguracją systemu
- Oddzielne pliki dla różnych aspektów (sieć, sprzęt, progi)
- Możliwość łatwej modyfikacji bez zmiany kodu

### 3. **GUI (`src/gui/`)**
- Framework Kivy dla interfejsu dotykowego
- Modularny system ekranów
- Niestandardowe widżety dla jachtów
- Optymalizacja pod Raspberry Pi

### 4. **Logika (`src/logic/`)**
- Moduły biznesowe dla każdego systemu jachtu
- Niezależne od GUI - możliwość testowania
- Implementacja algorytmów sterowania

### 5. **Sterowniki (`src/drivers/`)**
- Abstrakcja sprzętu Raspberry Pi
- Obsługa GPIO, UART, I2C, SPI
- Interfejsy dla różnych typów urządzeń

### 6. **Usługi (`src/services/`)**
- Usługi działające w tle
- Centralne zarządzanie danymi
- Monitoring systemu
- API i komunikacja sieciowa

### 7. **Narzędzia (`src/utils/`)**
- Funkcje pomocnicze
- System logowania
- Zarządzanie konfiguracją
- Obsługa błędów

### 8. **Testy (`tests/`)**
- Testy jednostkowe dla wszystkich modułów
- Framework pytest
- Pokrycie kodu testami

### 9. **Dokumentacja (`docs/`)**
- Kompletna dokumentacja systemu
- Instrukcje instalacji i konfiguracji
- Podręczniki użytkownika i developera

## Zalety tej struktury

1. **Modularność** - Każdy komponent jest niezależny
2. **Skalowalność** - Łatwe dodawanie nowych funkcji
3. **Testowalność** - Wszystkie moduły można testować osobno
4. **Czytelność** - Jasny podział odpowiedzialności
5. **Konfigurowalność** - Łatwa zmiana ustawień bez modyfikacji kodu
6. **Optymalizacja** - Dostosowane pod Raspberry Pi 5
7. **Bezpieczeństwo** - Oddzielenie logiki od interfejsu
8. **Rozszerzalność** - Możliwość dodawania nowych modułów

## Następne kroki

1. Implementacja brakujących modułów
2. Dodanie testów dla wszystkich komponentów
3. Konfiguracja CI/CD
4. Dokumentacja API
5. Optymalizacja wydajności
6. Testy na rzeczywistym sprzęcie
