# TridentOS - Yacht Control System

TridentOS is a comprehensive yacht control system designed for Raspberry Pi 5, providing an intuitive interface for managing all yacht systems including navigation, engine control, climate, lighting, and emergency systems.

## Features

- **Modular Architecture**: Clean separation of GUI, logic, and hardware drivers
- **Real-time Monitoring**: Live monitoring of all yacht systems
- **Touch Interface**: Optimized for touchscreen displays
- **Hardware Integration**: Direct control of GPIO, UART, I2C devices
- **Emergency Systems**: Dedicated emergency protocols and alerts
- **Autopilot**: Advanced navigation and steering control
- **Climate Control**: HVAC and environmental monitoring
- **Engine Management**: Engine monitoring and control
- **Lighting Control**: Interior and exterior lighting systems
- **Water Systems**: Fresh water, bilge, and waste management
- **Battery Management**: Power monitoring and distribution
- **Fuel Management**: Fuel level and consumption tracking

## System Requirements

- Raspberry Pi 5 (4GB+ RAM recommended)
- MicroSD card (32GB+ Class 10)
- Touchscreen display (7" or larger recommended)
- Python 3.11+
- Kivy 2.2.0+

## Hardware Connections

- **GPIO**: Direct control pins for relays, sensors
- **UART**: Communication with ESP32 modules
- **I2C**: Sensor communication (temperature, pressure, etc.)
- **SPI**: High-speed device communication
- **USB**: External device connectivity

## Installation

### Quick Setup
```bash
chmod +x setup.sh
./setup.sh
```

### Manual Installation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python dependencies
pip3 install -r requirements.txt

# Install system dependencies
sudo apt install -y python3-dev python3-pip libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev libsdl2-ttf-dev

# Configure auto-start (optional)
sudo systemctl enable tridentos.service
```

## Configuration

1. Copy `.env.example` to `.env` and configure your settings
2. Edit `config/system.yaml` for hardware-specific settings
3. Configure network settings in `config/network.yaml`

## Usage

### Start TridentOS
```bash
python3 main.py
```

### Development Mode
```bash
python3 main.py --debug
```

### Run Tests
```bash
python3 -m pytest tests/
```

## Project Structure

```
TridentOS/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── setup.sh               # Installation script
├── .env                   # Environment variables
├── README.md              # This file
├── config/                # Configuration files
├── src/                   # Source code
│   ├── gui/              # User interface
│   ├── logic/            # Business logic
│   ├── drivers/          # Hardware drivers
│   ├── services/         # System services
│   └── utils/            # Utilities
├── tests/                # Unit tests
├── docs/                 # Documentation
├── assets/               # Static assets
└── logs/                 # Application logs
```

## Development

### Adding New Modules
1. Create module in appropriate `src/` subdirectory
2. Add configuration in `config/`
3. Write tests in `tests/`
4. Update documentation

### Hardware Integration
- Use `src/drivers/` for hardware-specific code
- Follow the existing GPIO/UART/I2C patterns
- Test thoroughly on actual hardware

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

Copyright (c) 2024 TridentOS Team. All rights reserved.

## Support

For support and documentation, visit: [TridentOS Documentation](docs/)

## Version History

- **v1.0.0** - Initial release
  - Basic GUI framework
  - Hardware driver foundation
  - Core system modules
