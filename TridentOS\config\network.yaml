# TridentOS Network Configuration

# WiFi Configuration
wifi:
  ssid: "YachtNetwork"
  password: ""
  auto_connect: true
  backup_networks:
    - ssid: "Marina_WiFi"
      password: ""
    - ssid: "Mobile_Hotspot"
      password: ""

# Access Point (Hotspot) Configuration
hotspot:
  enabled: true
  ssid: "TridentOS-AP"
  password: "trident123"
  channel: 6
  hidden: false
  max_clients: 8
  ip_range: "***********/24"
  
# Network Services
services:
  web_interface:
    enabled: true
    port: 8080
    ssl: false
    
  api_server:
    enabled: true
    port: 8081
    ssl: false
    cors_enabled: true
    
  websocket:
    enabled: true
    port: 8082
    
  mqtt:
    enabled: false
    broker: "localhost"
    port: 1883
    username: ""
    password: ""
    topics:
      sensors: "tridentos/sensors"
      commands: "tridentos/commands"
      status: "tridentos/status"
      
# Remote Access
remote_access:
  ssh:
    enabled: true
    port: 22
    key_only: true
    
  vnc:
    enabled: false
    port: 5900
    password: ""
    
  ftp:
    enabled: false
    port: 21
    
# Security
security:
  firewall:
    enabled: true
    allowed_ports: [22, 80, 443, 8080, 8081, 8082]
    
  authentication:
    required: true
    session_timeout: 1800  # seconds
    max_failed_attempts: 5
    lockout_duration: 300  # seconds
    
  encryption:
    ssl_cert_path: "/etc/ssl/certs/tridentos.crt"
    ssl_key_path: "/etc/ssl/private/tridentos.key"
    
# External Services
external_services:
  weather_api:
    enabled: true
    provider: "openweathermap"
    api_key: ""
    update_interval: 1800  # seconds
    
  gps_service:
    enabled: true
    provider: "internal"
    backup_provider: "external_api"
    
  time_sync:
    enabled: true
    ntp_servers:
      - "pool.ntp.org"
      - "time.google.com"
      
# Monitoring
monitoring:
  network_check_interval: 30  # seconds
  connection_timeout: 10      # seconds
  retry_attempts: 3
  
  bandwidth_monitoring:
    enabled: true
    alert_threshold: 80  # percentage of available bandwidth
    
  latency_monitoring:
    enabled: true
    alert_threshold: 1000  # milliseconds
