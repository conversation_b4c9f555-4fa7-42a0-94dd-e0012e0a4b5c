# TridentOS System Configuration
system:
  name: "TridentOS"
  version: "1.0.0"
  debug: false
  log_level: "INFO"
  
display:
  width: 1920
  height: 1080
  fullscreen: true
  show_cursor: false
  orientation: "landscape"
  brightness: 80  # percentage
  
hardware:
  # GPIO Pin Configuration
  gpio_pins:
    # Engine Control
    engine_start_relay: 18
    engine_stop_relay: 19
    engine_cooling_fan: 20
    
    # Lighting
    navigation_lights: 21
    cabin_lights: 22
    deck_lights: 23
    emergency_lights: 24
    
    # Water Systems
    bilge_pump_1: 25
    bilge_pump_2: 26
    fresh_water_pump: 27
    
    # Safety
    horn: 28
    emergency_beacon: 29
    
    # Sensors (Input pins)
    engine_temp_sensor: 4
    oil_pressure_sensor: 5
    fuel_level_sensor: 6
    battery_voltage_sensor: 7
    
  # UART Configuration
  uart:
    primary_port: "/dev/ttyS0"
    esp32_port: "/dev/ttyUSB0"
    gps_port: "/dev/ttyUSB1"
    baudrate: 115200
    timeout: 1.0
    
  # I2C Configuration
  i2c:
    bus: 1
    devices:
      temperature_sensor: 0x48
      pressure_sensor: 0x77
      compass: 0x1E
      accelerometer: 0x53
      
  # SPI Configuration
  spi:
    bus: 0
    device: 0
    max_speed: 1000000
    
# Sensor Thresholds and Limits
thresholds:
  engine:
    temperature:
      normal_max: 85      # Celsius
      warning: 90
      critical: 95
    oil_pressure:
      normal_min: 25      # PSI
      warning: 20
      critical: 15
    rpm:
      idle: 800
      cruise: 2000
      max: 3500
      
  battery:
    voltage:
      full: 12.8          # Volts
      normal_min: 12.0
      warning: 11.5
      critical: 11.0
    charge_level:
      full: 100           # Percentage
      warning: 20
      critical: 10
      
  fuel:
    level:
      full: 100           # Percentage
      warning: 25
      critical: 10
    consumption_rate: 5   # Liters per hour at cruise
    
  water:
    fresh_water:
      full: 100           # Percentage
      warning: 20
      critical: 10
    bilge_level:
      normal: 10          # Percentage
      warning: 50
      critical: 80
      
  climate:
    temperature:
      comfort_min: 18     # Celsius
      comfort_max: 26
      warning_min: 5
      warning_max: 35
    humidity:
      comfort_max: 60     # Percentage
      warning_max: 80
      
# Update Intervals (seconds)
update_intervals:
  sensors: 1.0
  display: 0.5
  logging: 5.0
  system_check: 10.0
  backup: 3600.0        # 1 hour

# Safety Configuration
safety:
  emergency_contacts:
    - name: "Coast Guard"
      number: "+1-555-COAST"
      type: "emergency"
    - name: "Marina"
      number: "+1-555-MARINA"
      type: "support"
      
  auto_shutdown:
    engine_overheat: true
    low_oil_pressure: true
    bilge_overflow: true
    
  alarms:
    audio_enabled: true
    visual_enabled: true
    vibration_enabled: false
