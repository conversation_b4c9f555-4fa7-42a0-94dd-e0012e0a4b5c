2025-10-01 08:39:43 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:39:43 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:39:43 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:39:43 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:39:43 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:39:43 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x00000263DCD51B00>" was accessed, it will be removed in a future version
2025-10-01 08:39:43 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x00000263DCD51B00>" was accessed, it will be removed in a future version
2025-10-01 08:39:43 - kivy - ERROR - TridentOS: Failed to start application: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 75:
...
     73:        Line:
     74:            circle: (self.center_x, self.center_y, min(self.width, self.height)/2.5)
>>   75:            width: self.thickness
     76:        Color:
     77:            rgba: COLOR_ACCENT
...
TypeError: '<=' not supported between instances of 'NoneType' and 'int'
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 930, in _build_canvas
    setattr(instr, key, value)
    ~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "kivy\\graphics\\vertex_instructions_line.pxi", line 799, in kivy.graphics.vertex_instructions.Line.width.__set__

2025-10-01 08:40:44 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:40:44 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:40:44 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:40:44 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:40:44 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:40:44 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000002F5C8611B00>" was accessed, it will be removed in a future version
2025-10-01 08:40:44 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000002F5C8611B00>" was accessed, it will be removed in a future version
2025-10-01 08:40:44 - kivy - ERROR - TridentOS: Failed to start application: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 75:
...
     73:        Line:
     74:            circle: (self.center_x, self.center_y, min(self.width, self.height)/2.5)
>>   75:            width: self.thickness
     76:        Color:
     77:            rgba: COLOR_ACCENT
...
GraphicException: Invalid width value, must be > 0
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 930, in _build_canvas
    setattr(instr, key, value)
    ~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "kivy\\graphics\\vertex_instructions_line.pxi", line 800, in kivy.graphics.vertex_instructions.Line.width.__set__

2025-10-01 08:41:57 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:41:57 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:41:57 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:41:57 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:41:57 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:41:57 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x00000248D49E1A90>" was accessed, it will be removed in a future version
2025-10-01 08:41:57 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x00000248D49E1A90>" was accessed, it will be removed in a future version
2025-10-01 08:41:57 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:43:03 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:43:03 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:43:03 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:43:03 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:43:03 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:43:03 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000023FAC591A90>" was accessed, it will be removed in a future version
2025-10-01 08:43:03 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000023FAC591A90>" was accessed, it will be removed in a future version
2025-10-01 08:43:03 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:43:55 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:43:55 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:43:55 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:43:55 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:43:55 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:43:55 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000015F82101A90>" was accessed, it will be removed in a future version
2025-10-01 08:43:55 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000015F82101A90>" was accessed, it will be removed in a future version
2025-10-01 08:43:55 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:44:48 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:44:48 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:44:48 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:44:48 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:44:48 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:44:48 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x00000299422B1A20>" was accessed, it will be removed in a future version
2025-10-01 08:44:48 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x00000299422B1A20>" was accessed, it will be removed in a future version
2025-10-01 08:44:48 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:45:49 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:45:49 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:45:49 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:45:49 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:45:49 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:45:49 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000001F95C101B00>" was accessed, it will be removed in a future version
2025-10-01 08:45:49 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000001F95C101B00>" was accessed, it will be removed in a future version
2025-10-01 08:45:49 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:47:19 - src.services.system_monitor - INFO - System monitor stopped
2025-10-01 08:48:29 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:48:29 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:48:29 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:48:29 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:48:29 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:48:29 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000028FCC4817F0>" was accessed, it will be removed in a future version
2025-10-01 08:48:29 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000028FCC4817F0>" was accessed, it will be removed in a future version
2025-10-01 08:48:29 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:48:47 - src.services.system_monitor - INFO - System monitor stopped
2025-10-01 08:54:16 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:54:16 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:54:16 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:54:16 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:54:16 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:54:16 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000025321520FA0>" was accessed, it will be removed in a future version
2025-10-01 08:54:16 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000025321520FA0>" was accessed, it will be removed in a future version
2025-10-01 08:54:16 - src.services.data_service - INFO - Data service initialized
2025-10-01 11:05:55 - src.services.system_monitor - INFO - System monitor stopped
2025-10-01 11:09:54 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 11:09:54 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 11:09:54 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 11:09:54 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 11:09:54 - src.services.system_monitor - INFO - System monitor started
2025-10-01 11:09:54 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000002557CA310F0>" was accessed, it will be removed in a future version
2025-10-01 11:09:54 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000002557CA310F0>" was accessed, it will be removed in a future version
2025-10-01 11:09:54 - src.services.data_service - INFO - Data service initialized
2025-10-01 11:09:55 - src.services.system_monitor - WARNING - Memory usage high: 87.7%
2025-10-01 11:14:55 - src.services.system_monitor - WARNING - Memory usage high: 86.9%
2025-10-01 11:19:55 - src.services.system_monitor - WARNING - Memory usage high: 88.6%
2025-10-01 11:24:55 - src.services.system_monitor - WARNING - Memory usage high: 88.8%
2025-10-01 13:52:04 - src.services.system_monitor - WARNING - Memory usage high: 81.5%
2025-10-01 13:57:04 - src.services.system_monitor - WARNING - Memory usage high: 81.9%
2025-10-01 14:08:16 - src.services.system_monitor - WARNING - Memory usage high: 80.1%
2025-10-01 14:25:41 - src.services.system_monitor - WARNING - Memory usage high: 80.6%
2025-10-01 14:30:41 - src.services.system_monitor - WARNING - Memory usage high: 80.7%
2025-10-01 14:35:41 - src.services.system_monitor - WARNING - Memory usage high: 81.0%
2025-10-01 14:40:41 - src.services.system_monitor - WARNING - Memory usage high: 81.3%
2025-10-01 14:45:41 - src.services.system_monitor - WARNING - Memory usage high: 80.8%
2025-10-01 14:50:41 - src.services.system_monitor - WARNING - Memory usage high: 81.2%
2025-10-01 14:55:41 - src.services.system_monitor - WARNING - Memory usage high: 81.5%
2025-10-01 15:00:41 - src.services.system_monitor - WARNING - Memory usage high: 82.0%
2025-10-01 15:05:41 - src.services.system_monitor - WARNING - Memory usage high: 82.2%
2025-10-01 15:10:41 - src.services.system_monitor - WARNING - Memory usage high: 82.3%
2025-10-01 15:24:41 - src.services.system_monitor - WARNING - Memory usage high: 80.2%
2025-10-01 15:41:05 - src.services.system_monitor - WARNING - Memory usage high: 81.4%
2025-10-01 15:49:54 - src.services.system_monitor - WARNING - Memory usage high: 80.7%
2025-10-01 15:56:06 - src.services.system_monitor - WARNING - Memory usage high: 80.6%
2025-10-01 16:01:06 - src.services.system_monitor - WARNING - Memory usage high: 80.1%
2025-10-01 16:06:06 - src.services.system_monitor - WARNING - Memory usage high: 80.4%
2025-10-01 16:11:06 - src.services.system_monitor - WARNING - Memory usage high: 82.2%
2025-10-01 16:16:06 - src.services.system_monitor - WARNING - Memory usage high: 80.1%
2025-10-01 16:21:06 - src.services.system_monitor - WARNING - Memory usage high: 80.6%
2025-10-01 16:26:06 - src.services.system_monitor - WARNING - Memory usage high: 80.9%
2025-10-01 16:33:00 - src.services.system_monitor - WARNING - Memory usage high: 80.1%
2025-10-01 18:05:49 - src.services.system_monitor - WARNING - Memory usage high: 81.2%
2025-10-01 18:10:49 - src.services.system_monitor - WARNING - Memory usage high: 80.3%
2025-10-01 18:26:43 - src.services.system_monitor - WARNING - Memory usage high: 80.0%
2025-10-01 20:12:21 - src.services.system_monitor - WARNING - Memory usage high: 80.7%
2025-10-01 20:17:21 - src.services.system_monitor - WARNING - Memory usage high: 81.3%
2025-10-01 21:30:16 - src.services.system_monitor - WARNING - Memory usage high: 81.6%
2025-10-01 21:35:16 - src.services.system_monitor - WARNING - Memory usage high: 80.3%
2025-10-01 21:40:16 - src.services.system_monitor - WARNING - Memory usage high: 80.8%
2025-10-01 21:45:16 - src.services.system_monitor - WARNING - Memory usage high: 81.4%
2025-10-01 21:50:16 - src.services.system_monitor - WARNING - Memory usage high: 80.8%
2025-10-01 21:55:16 - src.services.system_monitor - WARNING - Memory usage high: 81.2%
2025-10-01 22:00:17 - src.services.system_monitor - WARNING - Memory usage high: 82.4%
2025-10-01 22:05:17 - src.services.system_monitor - WARNING - Memory usage high: 82.1%
2025-10-01 22:10:17 - src.services.system_monitor - WARNING - Memory usage high: 81.1%
2025-10-01 22:15:17 - src.services.system_monitor - WARNING - Memory usage high: 83.7%
2025-10-01 22:20:17 - src.services.system_monitor - WARNING - Memory usage high: 81.9%
2025-10-01 22:25:17 - src.services.system_monitor - WARNING - Memory usage high: 82.2%
2025-10-01 22:30:17 - src.services.system_monitor - WARNING - Memory usage high: 81.6%
2025-10-01 22:35:17 - src.services.system_monitor - WARNING - Memory usage high: 81.6%
2025-10-01 22:40:17 - src.services.system_monitor - WARNING - Memory usage high: 84.0%
2025-10-01 22:45:17 - src.services.system_monitor - WARNING - Memory usage high: 81.9%
2025-10-01 22:50:41 - src.services.system_monitor - WARNING - Memory usage high: 80.3%
2025-10-01 22:55:41 - src.services.system_monitor - WARNING - Memory usage high: 84.5%
2025-10-01 23:00:42 - src.services.system_monitor - WARNING - Memory usage high: 86.9%
2025-10-01 23:05:42 - src.services.system_monitor - WARNING - Memory usage high: 84.1%
2025-10-01 23:08:47 - src.services.system_monitor - INFO - System monitor stopped
2025-10-02 22:28:44 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-02 22:28:44 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-02 22:28:44 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-02 22:28:44 - src.services.system_monitor - INFO - System monitor initialized
2025-10-02 22:28:44 - src.services.system_monitor - INFO - System monitor started
2025-10-02 22:28:44 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x00000191D0200980>" was accessed, it will be removed in a future version
2025-10-02 22:28:44 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x00000191D0200980>" was accessed, it will be removed in a future version
2025-10-02 22:28:44 - src.services.data_service - INFO - Data service initialized
2025-10-02 22:28:45 - src.services.system_monitor - WARNING - Memory usage high: 80.3%
2025-10-02 22:31:56 - src.services.system_monitor - INFO - System monitor stopped
2025-10-02 22:34:56 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-02 22:34:56 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-02 22:34:56 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-02 22:34:56 - src.services.system_monitor - INFO - System monitor initialized
2025-10-02 22:34:56 - src.services.system_monitor - INFO - System monitor started
2025-10-02 22:34:56 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000001A541A90B40>" was accessed, it will be removed in a future version
2025-10-02 22:34:56 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000001A541A90B40>" was accessed, it will be removed in a future version
2025-10-02 22:34:56 - src.services.data_service - INFO - Data service initialized
2025-10-02 22:35:09 - src.services.system_monitor - WARNING - Memory usage high: 80.2%
2025-10-02 22:35:56 - src.services.system_monitor - INFO - System monitor stopped
2025-10-02 22:36:50 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-02 22:36:50 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-02 22:36:50 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-02 22:36:50 - src.services.system_monitor - INFO - System monitor initialized
2025-10-02 22:36:50 - src.services.system_monitor - INFO - System monitor started
2025-10-02 22:36:50 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000002107F9E0D00>" was accessed, it will be removed in a future version
2025-10-02 22:36:50 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000002107F9E0D00>" was accessed, it will be removed in a future version
2025-10-02 22:36:50 - src.services.data_service - INFO - Data service initialized
2025-10-02 22:36:51 - src.services.system_monitor - WARNING - Memory usage high: 80.1%
2025-10-02 22:38:14 - src.services.system_monitor - INFO - System monitor stopped
2025-10-02 22:39:29 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-02 22:39:29 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-02 22:39:29 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-02 22:39:29 - src.services.system_monitor - INFO - System monitor initialized
2025-10-02 22:39:29 - src.services.system_monitor - INFO - System monitor started
2025-10-02 22:39:29 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000001C7DE330D00>" was accessed, it will be removed in a future version
2025-10-02 22:39:29 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000001C7DE330D00>" was accessed, it will be removed in a future version
2025-10-02 22:39:29 - src.services.data_service - INFO - Data service initialized
2025-10-02 22:39:30 - src.services.system_monitor - WARNING - Memory usage high: 80.1%
2025-10-02 22:40:05 - src.services.system_monitor - INFO - System monitor stopped
2025-10-02 22:43:38 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-02 22:43:38 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-02 22:43:38 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-02 22:43:38 - src.services.system_monitor - INFO - System monitor initialized
2025-10-02 22:43:38 - src.services.system_monitor - INFO - System monitor started
2025-10-02 22:43:38 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000002CA9D330D70>" was accessed, it will be removed in a future version
2025-10-02 22:43:38 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000002CA9D330D70>" was accessed, it will be removed in a future version
2025-10-02 22:43:38 - src.services.data_service - INFO - Data service initialized
2025-10-02 22:43:39 - src.services.system_monitor - WARNING - Memory usage high: 82.1%
2025-10-02 22:44:26 - src.services.system_monitor - INFO - System monitor stopped
2025-10-02 22:45:15 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-02 22:45:15 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-02 22:45:15 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-02 22:45:15 - src.services.system_monitor - INFO - System monitor initialized
2025-10-02 22:45:15 - src.services.system_monitor - INFO - System monitor started
2025-10-02 22:45:15 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x00000149E4E808A0>" was accessed, it will be removed in a future version
2025-10-02 22:45:15 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x00000149E4E808A0>" was accessed, it will be removed in a future version
2025-10-02 22:45:15 - src.services.data_service - INFO - Data service initialized
2025-10-02 22:45:16 - src.services.system_monitor - WARNING - Memory usage high: 82.4%
2025-10-02 22:46:39 - src.services.system_monitor - INFO - System monitor stopped
2025-10-05 10:06:40 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-05 10:06:40 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-05 10:06:40 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-05 10:06:40 - src.services.system_monitor - INFO - System monitor initialized
2025-10-05 10:06:40 - src.services.system_monitor - INFO - System monitor started
2025-10-05 10:06:40 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000018DB1100B40>" was accessed, it will be removed in a future version
2025-10-05 10:06:40 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000018DB1100B40>" was accessed, it will be removed in a future version
2025-10-05 10:06:40 - src.services.data_service - INFO - Data service initialized
2025-10-05 10:06:41 - src.services.system_monitor - WARNING - Memory usage high: 91.9%
2025-10-05 10:09:09 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-05 10:09:09 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-05 10:09:09 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-05 10:09:09 - src.services.system_monitor - INFO - System monitor initialized
2025-10-05 10:09:09 - src.services.system_monitor - INFO - System monitor started
2025-10-05 10:09:09 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000001C07D1FCD70>" was accessed, it will be removed in a future version
2025-10-05 10:09:09 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000001C07D1FCD70>" was accessed, it will be removed in a future version
2025-10-05 10:09:09 - src.services.data_service - INFO - Data service initialized
2025-10-05 10:09:10 - src.services.system_monitor - WARNING - Memory usage high: 91.5%
2025-10-05 10:09:45 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-05 10:09:45 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-05 10:09:45 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-05 10:09:45 - src.services.system_monitor - INFO - System monitor initialized
2025-10-05 10:09:45 - src.services.system_monitor - INFO - System monitor started
2025-10-05 10:09:45 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000016CCFBBCD70>" was accessed, it will be removed in a future version
2025-10-05 10:09:45 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000016CCFBBCD70>" was accessed, it will be removed in a future version
2025-10-05 10:09:45 - src.services.data_service - INFO - Data service initialized
2025-10-05 10:09:46 - src.services.system_monitor - WARNING - Memory usage high: 91.5%
