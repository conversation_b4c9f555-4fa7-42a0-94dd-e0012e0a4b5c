#!/usr/bin/env python3
"""
TridentOS - Yacht Control System
Main application entry point for Raspberry Pi 5

Author: TridentOS Team
Version: 1.0.0
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Check if <PERSON>vy is available
KIVY_AVAILABLE = True
try:
    from kivy.app import App
    from kivy.config import Config
    from kivy.logger import Logger
    from kivy.lang import Builder
    from kivy.uix.label import Label as KivyLabel
    from kivy.properties import StringProperty
    from kivy.clock import Clock as KivyClock
    from kivy.resources import resource_add_path
    from kivy.core.window import Window
    from src.gui.main_window import MainWindow
except ImportError as e:
    KIVY_AVAILABLE = False
    print(f"Kivy not available: {e}")
    print("Running in development mode without GUI")

from src.utils.logger import setup_logging
from src.utils.config_manager import ConfigManager
from src.services.system_monitor import SystemMonitor

# Helper widget for KV clock
if KIVY_AVAILABLE:
    class ClockLabel(KivyLabel):
        """Digital clock label updating every second"""
        def __init__(self, **kwargs):
            super().__init__(**kwargs)
            self.text = ""
            KivyClock.schedule_interval(self._tick, 1)
        def _tick(self, dt):
            app = App.get_running_app()
            if app and hasattr(app, 'get_time'):
                self.text = app.get_time()



class TridentOSApp:
    """Main TridentOS Application - works with or without Kivy"""

    def __init__(self, **kwargs):
        if KIVY_AVAILABLE:
            # Initialize as Kivy App
            super().__init__(**kwargs)
        self.title = "TridentOS - Yacht Control System"
        self.config_manager = None
        self.system_monitor = None


    def get_time(self):
        """Return current time string for UI clock"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M")

    def build_config(self, config):
        """Build application configuration"""
        if KIVY_AVAILABLE:
            config.setdefaults('graphics', {
                'width': '1920',
                'height': '1080',
                'fullscreen': '1',
                'show_cursor': '0'
            })

    def build_settings(self, settings):
        """Build settings panel"""
        pass

    def build(self):
        """Build main application"""
        try:
            # Initialize configuration
            self.config_manager = ConfigManager()

            # Setup logging
            setup_logging()


            if KIVY_AVAILABLE:
                # Ensure Kivy can find resources (icons, kv files)
                try:
                    resource_add_path(str(PROJECT_ROOT))
                    resource_add_path(str(PROJECT_ROOT / 'src/gui/kv'))
                except Exception:
                    pass
                # Load KV UI files (home includes styles)
                try:
                    kv_path = PROJECT_ROOT / 'src/gui/kv/home.kv'
                    Logger.info(f"TridentOS: Loading KV from {kv_path}")
                    Builder.load_file(str(kv_path))
                    Logger.info("TridentOS: KV loaded successfully")
                except Exception as kv_err:
                    Logger.error(f"KV load error: {kv_err}")
                    import traceback
                    Logger.error(traceback.format_exc())

                Logger.info("TridentOS: Starting GUI application...")
                # Initialize system monitor
                self.system_monitor = SystemMonitor()
                self.system_monitor.start()

                # Return DashboardScreen directly (defined in KV)
                from kivy.uix.screenmanager import ScreenManager
                from src.gui.screens.dashboard import DashboardScreen

                sm = ScreenManager()
                dashboard = DashboardScreen(name='dashboard')
                sm.add_widget(dashboard)
                sm.current = 'dashboard'

                Logger.info("TridentOS: GUI application started successfully")
                return sm
            else:
                print("TridentOS: Starting in console mode...")
                # Initialize system monitor
                self.system_monitor = SystemMonitor()
                self.system_monitor.start()
                print("TridentOS: Console mode started successfully")
                return None

        except Exception as e:
            error_msg = f"TridentOS: Failed to start application: {e}"
            if KIVY_AVAILABLE:
                Logger.error(error_msg)
            else:
                print(error_msg)
            sys.exit(1)

    def on_stop(self):
        """Cleanup on application stop"""
        if KIVY_AVAILABLE:
            Logger.info("TridentOS: Shutting down...")
        else:
            print("TridentOS: Shutting down...")
        if self.system_monitor:
            self.system_monitor.stop()

    def run_console_mode(self):
        """Run in console mode without GUI"""
        print("=" * 50)
        print("TridentOS - Console Mode")
        print("=" * 50)

        try:
            self.build()

            print("System Status:")
            print(f"- Configuration Manager: {'OK' if self.config_manager else 'FAILED'}")
            print(f"- System Monitor: {'OK' if self.system_monitor else 'FAILED'}")

            # Show some system info
            if self.system_monitor:
                import time

            # Configure window (debug-friendly on Windows)
            if os.name == 'nt':
                Config.set('graphics', 'fullscreen', '0')
                Config.set('graphics', 'width', '1280')
                Config.set('graphics', 'height', '800')
                Config.set('graphics', 'show_cursor', '1')
                try:
                    Window.clearcolor = (0.043, 0.137, 0.188, 1)  # match COLOR_BG
                except Exception:
                    pass

                time.sleep(2)  # Let monitor collect some data
                metrics = self.system_monitor.get_current_metrics()
                if metrics:
                    print(f"- CPU Usage: {metrics.cpu_percent:.1f}%")
                    print(f"- Memory Usage: {metrics.memory_percent:.1f}%")
                    print(f"- Disk Usage: {metrics.disk_percent:.1f}%")
                    if metrics.temperature > 0:
                        print(f"- CPU Temperature: {metrics.temperature:.1f}°C")

            print("\nPress Ctrl+C to exit...")

            # Keep running until interrupted
            try:
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\nShutting down...")
                self.on_stop()

        except Exception as e:
            print(f"Error in console mode: {e}")
            sys.exit(1)


def main():
    """Main entry point"""
    try:
        if KIVY_AVAILABLE:
            # Make TridentOSApp inherit from Kivy App
            class TridentOSKivyApp(App, TridentOSApp):
                def __init__(self, **kwargs):
                    App.__init__(self, **kwargs)
                    TridentOSApp.__init__(self, **kwargs)

                def build(self):
                    """Override to call TridentOSApp.build()"""
                    return TridentOSApp.build(self)

                def on_stop(self):
                    """Override to call TridentOSApp.on_stop()"""
                    return TridentOSApp.on_stop(self)

            # Configure Kivy for Raspberry Pi (or Windows for dev)
            if os.name == 'nt':
                Config.set('graphics', 'fullscreen', '0')
                Config.set('graphics', 'width', '1280')
                Config.set('graphics', 'height', '800')
            else:
                Config.set('graphics', 'fullscreen', '1')
                Config.set('graphics', 'show_cursor', '0')
            Config.set('input', 'mouse', 'mouse,multitouch_on_demand')

            # Start GUI application
            app = TridentOSKivyApp()
            app.run()
        else:
            # Start console application
            app = TridentOSApp()
            app.run_console_mode()

    except KeyboardInterrupt:
        if KIVY_AVAILABLE:
            Logger.info("TridentOS: Interrupted by user")
        else:
            print("TridentOS: Interrupted by user")
    except Exception as e:
        error_msg = f"TridentOS: Fatal error: {e}"
        if KIVY_AVAILABLE:
            Logger.error(error_msg)
        else:
            print(error_msg)
        sys.exit(1)


if __name__ == "__main__":
    main()
