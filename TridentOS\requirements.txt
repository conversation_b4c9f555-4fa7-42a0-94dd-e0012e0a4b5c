# TridentOS Dependencies
# Core framework
kivy==2.2.0
kivymd==1.1.1

# Hardware interfaces
RPi.GPIO==0.7.1
pyserial==3.5
smbus2==0.4.2
spidev==3.6
gpiozero==1.6.2

# Networking and communication
requests==2.31.0
websockets==11.0.3
paho-mqtt==1.6.1
flask==2.3.3
flask-socketio==5.3.6

# Data handling
pyyaml==6.0.1
python-dotenv==1.0.0
jsonschema==4.19.1
pandas==2.1.1
numpy==1.24.3

# Database (optional)
sqlite3  # Built-in with Python
sqlalchemy==2.0.21

# Logging and monitoring
psutil==5.9.5
watchdog==3.0.0

# Time and scheduling
schedule==1.2.0
python-crontab==3.0.0

# Image processing
Pillow==10.0.1

# Testing
pytest==7.4.2
pytest-cov==4.1.0
pytest-mock==3.11.1

# Development tools
black==23.9.1
flake8==6.1.0
mypy==1.5.1

# System utilities
systemd-python==235  # For systemd integration on Raspberry Pi
