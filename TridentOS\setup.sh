#!/bin/bash
# TridentOS Installation Script for Raspberry Pi 5
# Run with: chmod +x setup.sh && ./setup.sh

set -e  # Exit on any error

echo "========================================="
echo "TridentOS Installation Script"
echo "Raspberry Pi 5 Yacht Control System"
echo "========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Check if running on Raspberry Pi
check_raspberry_pi() {
    log "Checking if running on Raspberry Pi..."
    if ! grep -q "Raspberry Pi" /proc/cpuinfo; then
        warn "This script is optimized for Raspberry Pi. Continuing anyway..."
    else
        log "Raspberry Pi detected!"
    fi
}

# Update system
update_system() {
    log "Updating system packages..."
    sudo apt update
    sudo apt upgrade -y
}

# Install system dependencies
install_system_deps() {
    log "Installing system dependencies..."
    sudo apt install -y \
        python3 \
        python3-pip \
        python3-dev \
        python3-venv \
        git \
        curl \
        wget \
        build-essential \
        cmake \
        pkg-config \
        libsdl2-dev \
        libsdl2-image-dev \
        libsdl2-mixer-dev \
        libsdl2-ttf-dev \
        libportmidi-dev \
        libswscale-dev \
        libavformat-dev \
        libavcodec-dev \
        zlib1g-dev \
        libgstreamer1.0-dev \
        gstreamer1.0-plugins-base \
        gstreamer1.0-plugins-good \
        libmtdev-dev \
        xclip \
        xsel
}

# Install Raspberry Pi specific packages
install_rpi_deps() {
    log "Installing Raspberry Pi specific packages..."
    sudo apt install -y \
        python3-rpi.gpio \
        i2c-tools \
        python3-smbus \
        python3-spidev \
        wiringpi
    
    # Enable I2C and SPI
    log "Enabling I2C and SPI interfaces..."
    sudo raspi-config nonint do_i2c 0
    sudo raspi-config nonint do_spi 0
    
    # Add user to gpio group
    sudo usermod -a -G gpio $USER
}

# Create virtual environment
create_venv() {
    log "Creating Python virtual environment..."
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip setuptools wheel
}

# Install Python dependencies
install_python_deps() {
    log "Installing Python dependencies..."
    source venv/bin/activate
    pip install -r requirements.txt
}

# Create directory structure
create_directories() {
    log "Creating directory structure..."
    mkdir -p logs
    mkdir -p data
    mkdir -p backups
    mkdir -p config
    mkdir -p assets/images
    mkdir -p assets/sounds
    mkdir -p docs
    mkdir -p tests
    mkdir -p src/{gui,logic,drivers,services,utils}
    
    # Set permissions
    chmod 755 logs data backups
}

# Copy configuration files
setup_config() {
    log "Setting up configuration files..."
    
    # Copy .env if it doesn't exist
    if [ ! -f .env ]; then
        cp .env.example .env 2>/dev/null || touch .env
        log "Created .env file - please configure it"
    fi
    
    # Create basic config files
    cat > config/system.yaml << EOF
# TridentOS System Configuration
system:
  name: "TridentOS"
  version: "1.0.0"
  debug: false

hardware:
  gpio_pins:
    engine_relay: 18
    lights_relay: 19
    bilge_pump: 20
    horn: 21
  
  uart:
    port: "/dev/ttyS0"
    baudrate: 115200
  
  i2c:
    bus: 1
    devices:
      temperature: 0x48
      pressure: 0x77

display:
  width: 1920
  height: 1080
  fullscreen: true
  show_cursor: false

logging:
  level: "INFO"
  file: "logs/tridentos.log"
  max_size: "10MB"
  backup_count: 5
EOF

    cat > config/network.yaml << EOF
# Network Configuration
wifi:
  ssid: "YachtNetwork"
  password: ""
  
hotspot:
  ssid: "TridentOS-AP"
  password: "trident123"
  
services:
  web_port: 8080
  api_port: 8081
  websocket_port: 8082
EOF
}

# Create systemd service
create_service() {
    log "Creating systemd service..."
    
    cat > tridentos.service << EOF
[Unit]
Description=TridentOS Yacht Control System
After=network.target
Wants=network.target

[Service]
Type=simple
User=$USER
Group=$USER
WorkingDirectory=$(pwd)
Environment=DISPLAY=:0
ExecStart=$(pwd)/venv/bin/python $(pwd)/main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    sudo mv tridentos.service /etc/systemd/system/
    sudo systemctl daemon-reload
    
    log "Service created. Enable with: sudo systemctl enable tridentos"
}

# Set up auto-start (optional)
setup_autostart() {
    read -p "Do you want to enable auto-start on boot? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo systemctl enable tridentos
        log "Auto-start enabled"
    fi
}

# Main installation function
main() {
    log "Starting TridentOS installation..."
    
    check_raspberry_pi
    update_system
    install_system_deps
    install_rpi_deps
    create_directories
    create_venv
    install_python_deps
    setup_config
    create_service
    setup_autostart
    
    log "========================================="
    log "TridentOS installation completed!"
    log "========================================="
    log "Next steps:"
    log "1. Configure .env file with your settings"
    log "2. Configure config/system.yaml and config/network.yaml"
    log "3. Test the installation: python3 main.py"
    log "4. Start service: sudo systemctl start tridentos"
    log "5. Check status: sudo systemctl status tridentos"
    log "========================================="
}

# Run main function
main "$@"
