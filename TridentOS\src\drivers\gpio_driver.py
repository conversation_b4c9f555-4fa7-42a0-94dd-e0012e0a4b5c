"""
TridentOS GPIO Driver
Raspberry Pi GPIO interface for relays, sensors, and digital I/O
"""

import logging
from typing import Dict, Optional, Callable
from enum import Enum
import threading
import time

logger = logging.getLogger(__name__)

try:
    import RPi.GPIO as GPIO
    GPIO_AVAILABLE = True
except ImportError:
    logger.warning("RPi.GPIO not available - using mock implementation")
    GPIO_AVAILABLE = False


class PinMode(Enum):
    """GPIO pin modes"""
    INPUT = "input"
    OUTPUT = "output"
    PWM = "pwm"


class PullMode(Enum):
    """GPIO pull resistor modes"""
    NONE = "none"
    UP = "up"
    DOWN = "down"


class MockGPIO:
    """Mock GPIO implementation for development/testing"""
    
    BCM = "BCM"
    OUT = "OUT"
    IN = "IN"
    HIGH = 1
    LOW = 0
    PUD_UP = "PUD_UP"
    PUD_DOWN = "PUD_DOWN"
    PUD_OFF = "PUD_OFF"
    RISING = "RISING"
    FALLING = "FALLING"
    BOTH = "BOTH"
    
    _pin_states = {}
    _pin_modes = {}
    
    @classmethod
    def setmode(cls, mode):
        pass
        
    @classmethod
    def setup(cls, pin, mode, pull_up_down=None):
        cls._pin_modes[pin] = mode
        if mode == cls.OUT:
            cls._pin_states[pin] = cls.LOW
            
    @classmethod
    def output(cls, pin, state):
        cls._pin_states[pin] = state
        logger.debug(f"Mock GPIO: Pin {pin} set to {state}")
        
    @classmethod
    def input(cls, pin):
        return cls._pin_states.get(pin, cls.LOW)
        
    @classmethod
    def PWM(cls, pin, frequency):
        return MockPWM(pin, frequency)
        
    @classmethod
    def add_event_detect(cls, pin, edge, callback=None, bouncetime=None):
        pass
        
    @classmethod
    def cleanup(cls):
        cls._pin_states.clear()
        cls._pin_modes.clear()


class MockPWM:
    """Mock PWM implementation"""
    
    def __init__(self, pin, frequency):
        self.pin = pin
        self.frequency = frequency
        self.duty_cycle = 0
        self.running = False
        
    def start(self, duty_cycle):
        self.duty_cycle = duty_cycle
        self.running = True
        logger.debug(f"Mock PWM: Pin {self.pin} started at {duty_cycle}%")
        
    def ChangeDutyCycle(self, duty_cycle):
        self.duty_cycle = duty_cycle
        logger.debug(f"Mock PWM: Pin {self.pin} duty cycle changed to {duty_cycle}%")
        
    def stop(self):
        self.running = False
        logger.debug(f"Mock PWM: Pin {self.pin} stopped")


class GPIODriver:
    """GPIO driver for Raspberry Pi hardware control"""
    
    def __init__(self):
        self.gpio = GPIO if GPIO_AVAILABLE else MockGPIO
        self.pins = {}
        self.pwm_instances = {}
        self.callbacks = {}
        self.initialized = False
        
        self._initialize()
        
    def _initialize(self):
        """Initialize GPIO system"""
        try:
            self.gpio.setmode(self.gpio.BCM)
            self.initialized = True
            logger.info("GPIO driver initialized")
        except Exception as e:
            logger.error(f"Failed to initialize GPIO: {e}")
            
    def setup_pin(self, pin: int, mode: PinMode, pull: PullMode = PullMode.NONE) -> bool:
        """Setup a GPIO pin"""
        try:
            if not self.initialized:
                return False
                
            gpio_mode = self.gpio.OUT if mode == PinMode.OUTPUT else self.gpio.IN
            
            # Set pull resistor
            pull_mode = self.gpio.PUD_OFF
            if pull == PullMode.UP:
                pull_mode = self.gpio.PUD_UP
            elif pull == PullMode.DOWN:
                pull_mode = self.gpio.PUD_DOWN
                
            self.gpio.setup(pin, gpio_mode, pull_up_down=pull_mode)
            
            self.pins[pin] = {
                'mode': mode,
                'pull': pull,
                'state': False
            }
            
            logger.debug(f"Pin {pin} setup as {mode.value} with pull {pull.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup pin {pin}: {e}")
            return False
            
    def set_output(self, pin: int, state: bool) -> bool:
        """Set output pin state"""
        try:
            if pin not in self.pins:
                logger.error(f"Pin {pin} not configured")
                return False
                
            if self.pins[pin]['mode'] != PinMode.OUTPUT:
                logger.error(f"Pin {pin} not configured as output")
                return False
                
            gpio_state = self.gpio.HIGH if state else self.gpio.LOW
            self.gpio.output(pin, gpio_state)
            self.pins[pin]['state'] = state
            
            logger.debug(f"Pin {pin} set to {state}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set pin {pin} output: {e}")
            return False
            
    def get_input(self, pin: int) -> Optional[bool]:
        """Read input pin state"""
        try:
            if pin not in self.pins:
                logger.error(f"Pin {pin} not configured")
                return None
                
            if self.pins[pin]['mode'] != PinMode.INPUT:
                logger.error(f"Pin {pin} not configured as input")
                return None
                
            state = self.gpio.input(pin) == self.gpio.HIGH
            self.pins[pin]['state'] = state
            return state
            
        except Exception as e:
            logger.error(f"Failed to read pin {pin}: {e}")
            return None
            
    def setup_pwm(self, pin: int, frequency: float) -> bool:
        """Setup PWM on a pin"""
        try:
            if pin not in self.pins or self.pins[pin]['mode'] != PinMode.OUTPUT:
                logger.error(f"Pin {pin} must be configured as output first")
                return False
                
            pwm = self.gpio.PWM(pin, frequency)
            self.pwm_instances[pin] = pwm
            self.pins[pin]['mode'] = PinMode.PWM
            
            logger.debug(f"PWM setup on pin {pin} at {frequency}Hz")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup PWM on pin {pin}: {e}")
            return False
            
    def set_pwm_duty_cycle(self, pin: int, duty_cycle: float) -> bool:
        """Set PWM duty cycle (0-100%)"""
        try:
            if pin not in self.pwm_instances:
                logger.error(f"PWM not configured on pin {pin}")
                return False
                
            duty_cycle = max(0, min(100, duty_cycle))
            
            pwm = self.pwm_instances[pin]
            if not hasattr(pwm, 'running') or not pwm.running:
                pwm.start(duty_cycle)
            else:
                pwm.ChangeDutyCycle(duty_cycle)
                
            logger.debug(f"PWM duty cycle on pin {pin} set to {duty_cycle}%")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set PWM duty cycle on pin {pin}: {e}")
            return False
            
    def add_interrupt(self, pin: int, edge: str, callback: Callable, 
                     bouncetime: int = 200) -> bool:
        """Add interrupt callback for pin"""
        try:
            if pin not in self.pins or self.pins[pin]['mode'] != PinMode.INPUT:
                logger.error(f"Pin {pin} must be configured as input")
                return False
                
            # Map edge types
            gpio_edge = self.gpio.BOTH
            if edge.lower() == 'rising':
                gpio_edge = self.gpio.RISING
            elif edge.lower() == 'falling':
                gpio_edge = self.gpio.FALLING
                
            self.gpio.add_event_detect(pin, gpio_edge, callback=callback, 
                                     bouncetime=bouncetime)
            
            self.callbacks[pin] = callback
            logger.debug(f"Interrupt added to pin {pin} for {edge} edge")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add interrupt to pin {pin}: {e}")
            return False
            
    def get_pin_status(self, pin: int) -> Optional[Dict]:
        """Get pin configuration and status"""
        if pin in self.pins:
            return self.pins[pin].copy()
        return None
        
    def get_all_pins_status(self) -> Dict:
        """Get status of all configured pins"""
        return {pin: info.copy() for pin, info in self.pins.items()}
        
    def cleanup(self):
        """Cleanup GPIO resources"""
        try:
            # Stop all PWM instances
            for pwm in self.pwm_instances.values():
                if hasattr(pwm, 'stop'):
                    pwm.stop()
                    
            self.pwm_instances.clear()
            self.callbacks.clear()
            self.pins.clear()
            
            if self.initialized:
                self.gpio.cleanup()
                
            logger.info("GPIO driver cleaned up")
            
        except Exception as e:
            logger.error(f"Error during GPIO cleanup: {e}")
            
    def __del__(self):
        """Destructor - ensure cleanup"""
        self.cleanup()
