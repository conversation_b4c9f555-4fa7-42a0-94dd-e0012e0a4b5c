#:include styles.kv

<DashboardScreen>:
    name: 'dashboard'
    BoxLayout:
        orientation: 'horizontal'
        canvas.before:
            Color:
                rgba: COLOR_BG
            Rectangle:
                pos: self.pos
                size: self.size

        # Left sidebar with icons
        BoxLayout:
            orientation: 'vertical'
            size_hint_x: None
            width: dp(70)
            padding: dp(10), dp(140)
            spacing: dp(50)

            # Settings icon
            Image:
                source: 'Icons/setting.png'
                size_hint: None, None
                size: dp(44), dp(44)
                color: COLOR_SUB

            # Stats icon
            Image:
                source: 'Icons/graph.png'
                size_hint: None, None
                size: dp(44), dp(44)
                color: COLOR_SUB

            # Tasks icon
            Image:
                source: 'Icons/file.png'
                size_hint: None, None
                size: dp(44), dp(44)
                color: COLOR_SUB

            Widget:  # Spacer

        # Main content area
        BoxLayout:
            orientation: 'vertical'
            padding: dp(40), dp(20), dp(40), dp(20)
            spacing: dp(20)

            # Top bar: title left + clock right
            BoxLayout:
                size_hint_y: None
                height: dp(60)
                TitleLabel:
                    text: 'PoseidonBox I'
                    size_hint_x: 0.7
                ClockLabel:
                    id: clock_lbl
                    text: app.get_time()
                    size_hint_x: 0.3
                    halign: 'right'
                    valign: 'middle'
                    text_size: self.size

            # Cards grid 2x4
            GridLayout:
                cols: 4
                rows: 2
                spacing: dp(20)
                size_hint_y: 1

                # LIGHTNING
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'LIGHTNING'
                        Widget:
                            size_hint_y: 0.1
                        Image:
                            source: 'Icons/bulb.png'
                            size_hint_y: 0.4
                            allow_stretch: True
                            keep_ratio: True
                        Widget:
                            size_hint_y: 0.05
                        BoxLayout:
                            size_hint_y: None
                            height: dp(32)
                            spacing: dp(8)
                            Label:
                                text: 'ON'
                                color: COLOR_TEXT
                                font_size: '18sp'
                                size_hint_x: 0.4
                                halign: 'center'
                                valign: 'middle'
                            Widget:
                                size_hint_x: 0.6
                                canvas.after:
                                    Color:
                                        rgba: COLOR_ACCENT
                                    RoundedRectangle:
                                        pos: self.center_x - dp(28), self.center_y - dp(10)
                                        size: dp(56), dp(20)
                                        radius: [dp(10),]
                                    Color:
                                        rgba: COLOR_TEXT
                                    Ellipse:
                                        pos: self.center_x + dp(8), self.center_y - dp(8)
                                        size: dp(16), dp(16)
                        Widget:
                            size_hint_y: 0.1

                # BATTERY
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'BATTERY'
                        Widget:
                            size_hint_y: 0.1
                        FloatLayout:
                            size_hint_y: 0.65
                            CircularProgress:
                                id: battery_circle
                                progress: 0.72
                                size_hint: None, None
                                size: min(self.parent.width, self.parent.height) * 0.8, min(self.parent.width, self.parent.height) * 0.8
                                pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                            Label:
                                text: '72%'
                                font_size: '26sp'
                                color: COLOR_TEXT
                                halign: 'center'
                                valign: 'middle'
                                pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                        Widget:
                            size_hint_y: 0.1

                # AUTOPILOT
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'AUTOPILOT'
                        FloatLayout:
                            Image:
                                source: 'Icons/compass.png'
                                size_hint: None, None
                                size: dp(60), dp(60)
                                pos_hint: {'center_x': 0.5, 'center_y': 0.65}
                                allow_stretch: True
                                keep_ratio: True
                            ActionButton:
                                text: 'Engage'
                                size_hint: None, None
                                size: dp(100), dp(32)
                                pos_hint: {'center_x': 0.5, 'center_y': 0.25}
                                on_release: app.root.show_navigation(None) if hasattr(app.root, 'show_navigation') else None

                # EMERGENCY
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'EMERGENCY'
                        Widget:
                            size_hint_y: 0.1
                        Image:
                            source: 'Icons/emergency.png'
                            size_hint_y: 0.65
                            allow_stretch: True
                            keep_ratio: True
                            color: COLOR_EMERGENCY
                        Widget:
                            size_hint_y: 0.1

                # ENGINE
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'ENGINE'
                        Widget:
                            size_hint_y: 0.1
                        Image:
                            source: 'Icons/engine.png'
                            size_hint_y: 0.45
                            allow_stretch: True
                            keep_ratio: True
                        Widget:
                            size_hint_y: 0.05
                        Label:
                            text: 'running'
                            color: COLOR_ACCENT
                            font_size: '18sp'
                            size_hint_y: None
                            height: dp(28)
                            halign: 'center'
                            valign: 'middle'
                        Widget:
                            size_hint_y: 0.1

                # CLIMATE
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'CLIMATE'
                        Widget:
                            size_hint_y: 0.15
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: 0.55
                            spacing: dp(4)
                            Label:
                                text: '26 °C'
                                font_size: '30sp'
                                color: COLOR_TEXT
                                halign: 'center'
                                valign: 'middle'
                            Label:
                                text: '30 %'
                                font_size: '22sp'
                                color: COLOR_SUB
                                halign: 'center'
                                valign: 'middle'
                        Widget:
                            size_hint_y: 0.1

                # WATER
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'WATER'
                        Widget:
                            size_hint_y: 0.1
                        Image:
                            source: 'Icons/drop.png'
                            size_hint_y: 0.35
                            allow_stretch: True
                            keep_ratio: True
                        Widget:
                            size_hint_y: 0.05
                        ThinProgress:
                            progress: 0.8
                            size_hint_y: None
                            height: dp(6)
                        Widget:
                            size_hint_y: 0.03
                        Label:
                            text: '80%'
                            font_size: '18sp'
                            color: COLOR_TEXT
                            size_hint_y: None
                            height: dp(24)
                            halign: 'center'
                            valign: 'middle'
                        Widget:
                            size_hint_y: 0.1

                # FUEL
                Card:
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        CardTitle:
                            text: 'FUEL'
                        Widget:
                            size_hint_y: 0.1
                        FloatLayout:
                            size_hint_y: 0.65
                            CircularProgress:
                                progress: 0.72
                                size_hint: None, None
                                size: min(self.parent.width, self.parent.height) * 0.8, min(self.parent.width, self.parent.height) * 0.8
                                pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                            Label:
                                text: '72%'
                                font_size: '26sp'
                                color: COLOR_TEXT
                                halign: 'center'
                                valign: 'middle'
                                pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                        Widget:
                            size_hint_y: 0.1

