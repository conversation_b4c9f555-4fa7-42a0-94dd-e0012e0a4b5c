#:import rgba kivy.utils.get_color_from_hex

# Global color palette (dark teal theme) - matched exactly to image
#:set COLOR_BG rgba('#0B2330')
#:set COLOR_PANEL rgba('#132C3A')
#:set COLOR_CARD rgba('#0A1A25')
#:set COLOR_CARD_BORDER rgba('#1A3D4F')
#:set COLOR_ACCENT rgba('#00D5D5')
#:set COLOR_ACCENT_DARK rgba('#00A3A3')
#:set COLOR_TEXT rgba('#FFFFFF')
#:set COLOR_SUB rgba('#7FA8B0')
#:set COLOR_WARN rgba('#E85A5A')
#:set COLOR_EMERGENCY rgba('#E85A5A')

<TitleLabel@Label>:
    color: COLOR_SUB
    font_size: '32sp'
    bold: False
    halign: 'left'
    valign: 'middle'
    text_size: self.size

<ClockLabel>:
    color: COLOR_TEXT
    font_size: '52sp'
    bold: False

<Card@BoxLayout>:
    padding: dp(14)
    spacing: dp(6)
    canvas.before:
        Color:
            rgba: COLOR_CARD
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [dp(10),]
        Color:
            rgba: COLOR_CARD_BORDER
        Line:
            rounded_rectangle: (self.x, self.y, self.width, self.height, dp(10))
            width: 1

<CardTitle@Label>:
    color: COLOR_SUB
    font_size: '16sp'
    bold: False
    size_hint_y: None
    height: dp(24)
    halign: 'center'
    valign: 'middle'

<ActionButton@Button>:
    background_normal: ''
    background_color: 0,0,0,0
    color: COLOR_TEXT
    font_size: '16sp'
    bold: False
    canvas.before:
        Color:
            rgba: COLOR_ACCENT
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [dp(6),]

# Simple circular progress indicator
<CircularProgress@Widget>:
    progress: 0.72   # 0..1
    canvas.after:
        Color:
            rgba: rgba('#0C3B49')
        Line:
            circle: (self.center_x, self.center_y, min(self.width, self.height)/2.2)
            width: 8
        Color:
            rgba: COLOR_ACCENT
        Line:
            circle: (self.center_x, self.center_y, min(self.width, self.height)/2.2, 0, self.progress * 360)
            width: 8
            cap: 'none'

# Horizontal thin progress (for WATER)
<ThinProgress@Widget>:
    progress: 0.8
    canvas.before:
        Color:
            rgba: rgba('#0C3B49')
        RoundedRectangle:
            pos: self.x + dp(8), self.center_y - dp(3)
            size: self.width - dp(16), dp(6)
            radius: [dp(3),]
        Color:
            rgba: COLOR_ACCENT
        RoundedRectangle:
            pos: self.x + dp(8), self.center_y - dp(3)
            size: (self.width - dp(16)) * self.progress, dp(6)
            radius: [dp(3),]

