"""
TridentOS Main Window
Primary application interface
"""

# Check if <PERSON><PERSON> is available
try:
    from kivy.uix.boxlayout import BoxLayout
    from kivy.uix.screenmanager import ScreenManager, Screen
    from kivy.uix.button import Button
    from kivy.uix.label import Label
    from kivy.clock import Clock
    from kivy.logger import Logger
    KIVY_AVAILABLE = True
except ImportError:
    KIVY_AVAILABLE = False
    # Mock classes for development without Kivy
    class BoxLayout:
        def __init__(self, **kwargs):
            self.orientation = kwargs.get('orientation', 'vertical')
        def add_widget(self, widget):
            pass

    class ScreenManager:
        def __init__(self, **kwargs):
            self.current = 'dashboard'
            self.current_screen = None
        def add_widget(self, widget):
            pass

    class Screen:
        def __init__(self, **kwargs):
            self.name = kwargs.get('name', '')
        def add_widget(self, widget):
            pass

    class Button:
        def __init__(self, **kwargs):
            self.text = kwargs.get('text', '')

    class Label:
        def __init__(self, **kwargs):
            self.text = kwargs.get('text', '')
            self.color = kwargs.get('color', (1, 1, 1, 1))

    class Clock:
        @staticmethod
        def schedule_interval(func, interval):
            pass

try:
    from .screens.dashboard import DashboardScreen
    from .screens.navigation import NavigationScreen
    from .screens.engine import EngineScreen
    from .screens.climate import ClimateScreen
    from .screens.lighting import LightingScreen
    from .screens.water import WaterScreen
    from .screens.battery import BatteryScreen
    from .screens.emergency import EmergencyScreen
    from .screens.settings import SettingsScreen
except ImportError:
    # Mock screen classes
    class DashboardScreen(Screen):
        pass
    class NavigationScreen(Screen):
        pass
    class EngineScreen(Screen):
        pass
    class ClimateScreen(Screen):
        pass
    class LightingScreen(Screen):
        pass
    class WaterScreen(Screen):
        pass
    class BatteryScreen(Screen):
        pass
    class EmergencyScreen(Screen):
        pass
    class SettingsScreen(Screen):
        pass


class MainWindow(BoxLayout):
    """Main application window with navigation and screen management"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'horizontal'
        
        # Initialize screen manager
        self.screen_manager = ScreenManager()
        
        # Create navigation sidebar
        self.create_navigation()
        
        # Create screens
        self.create_screens()

        try:
            Logger.info(f"MainWindow: Screens available: {getattr(self.screen_manager, 'screens', [])}")
            Logger.info(f"MainWindow: Current screen before add: {self.screen_manager.current}")
        except Exception:
            pass

        # Add components to layout
        self.add_widget(self.navigation_panel)
        self.add_widget(self.screen_manager)

        # Start update clock
        Clock.schedule_interval(self.update_display, 1.0)

    def create_navigation(self):
        """Create navigation sidebar"""
        self.navigation_panel = BoxLayout(
            orientation='vertical',
            size_hint_x=0.15,
            spacing=10,
            padding=10
        )
        
        # Navigation buttons
        nav_buttons = [
            ('Dashboard', 'dashboard', self.show_dashboard),
            ('Navigation', 'navigation', self.show_navigation),
            ('Engine', 'engine', self.show_engine),
            ('Climate', 'climate', self.show_climate),
            ('Lighting', 'lighting', self.show_lighting),
            ('Water', 'water', self.show_water),
            ('Battery', 'battery', self.show_battery),
            ('Emergency', 'emergency', self.show_emergency),
            ('Settings', 'settings', self.show_settings),
        ]
        
        for text, screen_name, callback in nav_buttons:
            btn = Button(
                text=text,
                size_hint_y=None,
                height=60,
                on_press=callback
            )
            self.navigation_panel.add_widget(btn)
            
        # Add spacer
        self.navigation_panel.add_widget(Label())
        
        # System status
        self.status_label = Label(
            text="System: OK",
            size_hint_y=None,
            height=40,
            color=(0, 1, 0, 1)  # Green
        )
        self.navigation_panel.add_widget(self.status_label)
        
    def create_screens(self):
        """Create all application screens"""
        screens = [
            ('dashboard', DashboardScreen),
            ('navigation', NavigationScreen),
            ('engine', EngineScreen),
            ('climate', ClimateScreen),
            ('lighting', LightingScreen),
            ('water', WaterScreen),
            ('battery', BatteryScreen),
            ('emergency', EmergencyScreen),
            ('settings', SettingsScreen),
        ]
        
        for screen_name, screen_class in screens:
            try:
                screen = screen_class(name=screen_name)
                self.screen_manager.add_widget(screen)
                Logger.info(f"MainWindow: Added screen '{screen_name}' ({screen_class.__name__})")
            except Exception as e:
                Logger.error(f"MainWindow: Failed to create {screen_name} screen: {e}")
                # Create fallback screen
                fallback = Screen(name=screen_name)
                fallback.add_widget(Label(text=f"{screen_name.title()} Screen\n(Under Development)"))
                self.screen_manager.add_widget(fallback)

        # Set default screen
        self.screen_manager.current = 'dashboard'
        Logger.info(f"MainWindow: Current screen set to: {self.screen_manager.current}")

    def show_dashboard(self, instance):
        """Show dashboard screen"""
        self.screen_manager.current = 'dashboard'
        
    def show_navigation(self, instance):
        """Show navigation screen"""
        self.screen_manager.current = 'navigation'
        
    def show_engine(self, instance):
        """Show engine screen"""
        self.screen_manager.current = 'engine'
        
    def show_climate(self, instance):
        """Show climate screen"""
        self.screen_manager.current = 'climate'
        
    def show_lighting(self, instance):
        """Show lighting screen"""
        self.screen_manager.current = 'lighting'
        
    def show_water(self, instance):
        """Show water screen"""
        self.screen_manager.current = 'water'
        
    def show_battery(self, instance):
        """Show battery screen"""
        self.screen_manager.current = 'battery'
        
    def show_emergency(self, instance):
        """Show emergency screen"""
        self.screen_manager.current = 'emergency'
        
    def show_settings(self, instance):
        """Show settings screen"""
        self.screen_manager.current = 'settings'
        
    def update_display(self, dt):
        """Update display elements periodically"""
        try:
            # Update system status
            # This would connect to actual system monitoring
            self.status_label.text = "System: OK"
            self.status_label.color = (0, 1, 0, 1)  # Green
            
            # Update current screen if it has an update method
            current_screen = self.screen_manager.current_screen
            if hasattr(current_screen, 'update_display'):
                current_screen.update_display()
                
        except Exception as e:
            Logger.error(f"MainWindow: Display update error: {e}")
            self.status_label.text = "System: Error"
            self.status_label.color = (1, 0, 0, 1)  # Red
