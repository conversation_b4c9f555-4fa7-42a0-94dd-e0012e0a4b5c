"""
TridentOS Circular Gauge Widget
Displays values in circular gauge format
"""

from kivy.uix.widget import Widget
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.graphics import Color, Line, Ellipse
from math import cos, sin, pi


class CircularGauge(BoxLayout):
    """Circular gauge widget for displaying numeric values"""
    
    def __init__(self, title="", min_value=0, max_value=100, current_value=0, 
                 unit="", warning_threshold=None, critical_threshold=None, 
                 is_compass=False, **kwargs):
        super().__init__(**kwargs)
        
        self.orientation = 'vertical'
        self.spacing = 10
        
        # Store parameters
        self.title = title
        self.min_value = min_value
        self.max_value = max_value
        self.current_value = current_value
        self.unit = unit
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.is_compass = is_compass
        
        # Create title
        self.title_label = Label(
            text=title,
            size_hint_y=0.1,
            font_size=18,
            bold=True
        )
        self.add_widget(self.title_label)
        
        # Create gauge widget
        self.gauge_widget = GaugeWidget(
            min_value=min_value,
            max_value=max_value,
            current_value=current_value,
            warning_threshold=warning_threshold,
            critical_threshold=critical_threshold,
            is_compass=is_compass
        )
        self.add_widget(self.gauge_widget)
        
        # Create value label
        self.value_label = Label(
            text=f"{current_value} {unit}",
            size_hint_y=0.1,
            font_size=16,
            bold=True
        )
        self.add_widget(self.value_label)
        
    def update_value(self, value):
        """Update gauge value"""
        self.current_value = value
        self.gauge_widget.update_value(value)
        self.value_label.text = f"{value} {self.unit}"


class GaugeWidget(Widget):
    """Internal gauge drawing widget"""
    
    def __init__(self, min_value=0, max_value=100, current_value=0,
                 warning_threshold=None, critical_threshold=None,
                 is_compass=False, **kwargs):
        super().__init__(**kwargs)
        
        self.min_value = min_value
        self.max_value = max_value
        self.current_value = current_value
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.is_compass = is_compass
        
        self.bind(pos=self.draw_gauge, size=self.draw_gauge)
        
    def draw_gauge(self, *args):
        """Draw the gauge"""
        self.canvas.clear()
        
        if self.size[0] == 0 or self.size[1] == 0:
            return
            
        center_x = self.center_x
        center_y = self.center_y
        radius = min(self.width, self.height) * 0.4
        
        with self.canvas:
            # Draw outer circle
            Color(0.3, 0.3, 0.3, 1)  # Dark gray
            Line(circle=(center_x, center_y, radius), width=3)
            
            # Draw scale marks
            self.draw_scale_marks(center_x, center_y, radius)
            
            # Draw colored arcs for thresholds
            if not self.is_compass:
                self.draw_threshold_arcs(center_x, center_y, radius)
            
            # Draw needle
            self.draw_needle(center_x, center_y, radius)
            
            # Draw center dot
            Color(0.2, 0.2, 0.2, 1)
            Ellipse(pos=(center_x - 5, center_y - 5), size=(10, 10))
            
    def draw_scale_marks(self, center_x, center_y, radius):
        """Draw scale marks around the gauge"""
        Color(0.6, 0.6, 0.6, 1)  # Light gray
        
        # Draw major marks
        for i in range(0, 9):  # 8 major divisions
            angle = -pi + (i * pi / 4)  # -180 to 0 degrees
            x1 = center_x + (radius - 10) * cos(angle)
            y1 = center_y + (radius - 10) * sin(angle)
            x2 = center_x + radius * cos(angle)
            y2 = center_y + radius * sin(angle)
            Line(points=[x1, y1, x2, y2], width=2)
            
    def draw_threshold_arcs(self, center_x, center_y, radius):
        """Draw colored arcs for warning and critical thresholds"""
        if self.warning_threshold is not None:
            # Warning arc (yellow)
            Color(1, 1, 0, 0.3)  # Semi-transparent yellow
            warning_angle = self.value_to_angle(self.warning_threshold)
            # Draw warning arc implementation would go here
            
        if self.critical_threshold is not None:
            # Critical arc (red)
            Color(1, 0, 0, 0.3)  # Semi-transparent red
            critical_angle = self.value_to_angle(self.critical_threshold)
            # Draw critical arc implementation would go here
            
    def draw_needle(self, center_x, center_y, radius):
        """Draw the gauge needle"""
        angle = self.value_to_angle(self.current_value)
        
        # Determine needle color based on thresholds
        if (self.critical_threshold and self.current_value >= self.critical_threshold):
            Color(1, 0, 0, 1)  # Red
        elif (self.warning_threshold and self.current_value >= self.warning_threshold):
            Color(1, 1, 0, 1)  # Yellow
        else:
            Color(0, 1, 0, 1)  # Green
            
        # Draw needle
        needle_length = radius * 0.8
        end_x = center_x + needle_length * cos(angle)
        end_y = center_y + needle_length * sin(angle)
        
        Line(points=[center_x, center_y, end_x, end_y], width=3)
        
    def value_to_angle(self, value):
        """Convert value to angle for needle position"""
        if self.is_compass:
            # For compass, 0 degrees is north (top)
            return (value * pi / 180) - (pi / 2)
        else:
            # For regular gauges, map value to -180 to 0 degrees
            ratio = (value - self.min_value) / (self.max_value - self.min_value)
            return -pi + (ratio * pi)
            
    def update_value(self, value):
        """Update the gauge value"""
        self.current_value = max(self.min_value, min(self.max_value, value))
        self.draw_gauge()
