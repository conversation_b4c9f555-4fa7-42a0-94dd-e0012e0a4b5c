"""
TridentOS Status Card Widget
Displays system status information in card format
"""

from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.graphics import Color, RoundedRectangle
from kivy.uix.widget import Widget


class StatusCard(BoxLayout):
    """Status card widget for displaying system information"""
    
    def __init__(self, title="", value="", unit="", color=(1, 1, 1, 1), **kwargs):
        super().__init__(**kwargs)
        
        self.orientation = 'vertical'
        self.padding = 10
        self.spacing = 5
        
        # Store initial values
        self.card_color = color
        
        # Create background
        with self.canvas.before:
            Color(*color)
            self.bg_rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[10]
            )
        
        # Bind to update background
        self.bind(pos=self.update_bg, size=self.update_bg)
        
        # Title label
        self.title_label = Label(
            text=title,
            font_size=16,
            bold=True,
            size_hint_y=0.3,
            color=(0, 0, 0, 1)  # Black text
        )
        self.add_widget(self.title_label)
        
        # Value container
        value_layout = BoxLayout(orientation='horizontal', size_hint_y=0.7)
        
        # Value label
        self.value_label = Label(
            text=value,
            font_size=32,
            bold=True,
            color=(0, 0, 0, 1)  # Black text
        )
        value_layout.add_widget(self.value_label)
        
        # Unit label
        self.unit_label = Label(
            text=unit,
            font_size=16,
            size_hint_x=0.3,
            color=(0, 0, 0, 1)  # Black text
        )
        value_layout.add_widget(self.unit_label)
        
        self.add_widget(value_layout)
        
    def update_bg(self, *args):
        """Update background rectangle"""
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size
        
    def update_value(self, value, color=None):
        """Update the displayed value"""
        self.value_label.text = str(value)
        if color:
            self.update_color(color)
            
    def update_color(self, color):
        """Update card background color"""
        self.card_color = color
        with self.canvas.before:
            Color(*color)
            self.bg_rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[10]
            )
