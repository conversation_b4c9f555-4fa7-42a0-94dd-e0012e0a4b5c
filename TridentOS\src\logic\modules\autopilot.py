"""
TridentOS Autopilot Module
Navigation and steering control logic
"""

import logging
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class AutopilotMode(Enum):
    """Autopilot operating modes"""
    OFF = "off"
    COMPASS = "compass"
    GPS = "gps"
    WIND = "wind"


@dataclass
class NavigationData:
    """Navigation data structure"""
    latitude: float = 0.0
    longitude: float = 0.0
    heading: float = 0.0
    speed: float = 0.0
    course: float = 0.0
    wind_direction: float = 0.0
    wind_speed: float = 0.0


@dataclass
class AutopilotSettings:
    """Autopilot configuration settings"""
    mode: AutopilotMode = AutopilotMode.OFF
    target_heading: float = 0.0
    target_latitude: float = 0.0
    target_longitude: float = 0.0
    max_rudder_angle: float = 30.0
    response_sensitivity: float = 1.0
    course_tolerance: float = 5.0


class AutopilotModule:
    """Autopilot control and navigation module"""
    
    def __init__(self):
        self.settings = AutopilotSettings()
        self.navigation_data = NavigationData()
        self.is_active = False
        self.last_correction = 0.0
        
        logger.info("Autopilot module initialized")
        
    def set_mode(self, mode: AutopilotMode) -> bool:
        """Set autopilot mode"""
        try:
            self.settings.mode = mode
            logger.info(f"Autopilot mode set to: {mode.value}")
            return True
        except Exception as e:
            logger.error(f"Failed to set autopilot mode: {e}")
            return False
            
    def set_target_heading(self, heading: float) -> bool:
        """Set target heading for compass mode"""
        try:
            if 0 <= heading <= 360:
                self.settings.target_heading = heading
                logger.info(f"Target heading set to: {heading}°")
                return True
            else:
                logger.error(f"Invalid heading: {heading}")
                return False
        except Exception as e:
            logger.error(f"Failed to set target heading: {e}")
            return False
            
    def set_target_position(self, latitude: float, longitude: float) -> bool:
        """Set target position for GPS mode"""
        try:
            if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                self.settings.target_latitude = latitude
                self.settings.target_longitude = longitude
                logger.info(f"Target position set to: {latitude}, {longitude}")
                return True
            else:
                logger.error(f"Invalid coordinates: {latitude}, {longitude}")
                return False
        except Exception as e:
            logger.error(f"Failed to set target position: {e}")
            return False
            
    def update_navigation_data(self, data: Dict) -> None:
        """Update navigation data from sensors"""
        try:
            self.navigation_data.latitude = data.get('latitude', 0.0)
            self.navigation_data.longitude = data.get('longitude', 0.0)
            self.navigation_data.heading = data.get('heading', 0.0)
            self.navigation_data.speed = data.get('speed', 0.0)
            self.navigation_data.course = data.get('course', 0.0)
            self.navigation_data.wind_direction = data.get('wind_direction', 0.0)
            self.navigation_data.wind_speed = data.get('wind_speed', 0.0)
        except Exception as e:
            logger.error(f"Failed to update navigation data: {e}")
            
    def engage(self) -> bool:
        """Engage autopilot"""
        try:
            if self.settings.mode == AutopilotMode.OFF:
                logger.warning("Cannot engage autopilot: mode is OFF")
                return False
                
            self.is_active = True
            logger.info("Autopilot engaged")
            return True
        except Exception as e:
            logger.error(f"Failed to engage autopilot: {e}")
            return False
            
    def disengage(self) -> bool:
        """Disengage autopilot"""
        try:
            self.is_active = False
            logger.info("Autopilot disengaged")
            return True
        except Exception as e:
            logger.error(f"Failed to disengage autopilot: {e}")
            return False
            
    def calculate_steering_correction(self) -> float:
        """Calculate steering correction based on current mode"""
        if not self.is_active:
            return 0.0
            
        try:
            if self.settings.mode == AutopilotMode.COMPASS:
                return self._calculate_compass_correction()
            elif self.settings.mode == AutopilotMode.GPS:
                return self._calculate_gps_correction()
            elif self.settings.mode == AutopilotMode.WIND:
                return self._calculate_wind_correction()
            else:
                return 0.0
        except Exception as e:
            logger.error(f"Failed to calculate steering correction: {e}")
            return 0.0
            
    def _calculate_compass_correction(self) -> float:
        """Calculate correction for compass mode"""
        heading_error = self.settings.target_heading - self.navigation_data.heading
        
        # Normalize error to -180 to 180 degrees
        if heading_error > 180:
            heading_error -= 360
        elif heading_error < -180:
            heading_error += 360
            
        # Apply sensitivity and limits
        correction = heading_error * self.settings.response_sensitivity
        correction = max(-self.settings.max_rudder_angle, 
                        min(self.settings.max_rudder_angle, correction))
        
        self.last_correction = correction
        return correction
        
    def _calculate_gps_correction(self) -> float:
        """Calculate correction for GPS mode"""
        # Calculate bearing to target
        target_bearing = self._calculate_bearing(
            self.navigation_data.latitude,
            self.navigation_data.longitude,
            self.settings.target_latitude,
            self.settings.target_longitude
        )
        
        # Calculate heading error
        heading_error = target_bearing - self.navigation_data.heading
        
        # Normalize error
        if heading_error > 180:
            heading_error -= 360
        elif heading_error < -180:
            heading_error += 360
            
        # Apply sensitivity and limits
        correction = heading_error * self.settings.response_sensitivity
        correction = max(-self.settings.max_rudder_angle,
                        min(self.settings.max_rudder_angle, correction))
        
        self.last_correction = correction
        return correction
        
    def _calculate_wind_correction(self) -> float:
        """Calculate correction for wind mode"""
        # Simplified wind-relative steering
        # This would be more complex in a real implementation
        wind_relative_heading = self.navigation_data.heading - self.navigation_data.wind_direction
        
        # Maintain constant wind angle
        target_wind_angle = 45.0  # Example: 45 degrees off the wind
        wind_error = target_wind_angle - wind_relative_heading
        
        correction = wind_error * self.settings.response_sensitivity * 0.5
        correction = max(-self.settings.max_rudder_angle,
                        min(self.settings.max_rudder_angle, correction))
        
        self.last_correction = correction
        return correction
        
    def _calculate_bearing(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate bearing between two GPS coordinates"""
        import math
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lon_rad = math.radians(lon2 - lon1)
        
        y = math.sin(delta_lon_rad) * math.cos(lat2_rad)
        x = (math.cos(lat1_rad) * math.sin(lat2_rad) - 
             math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(delta_lon_rad))
        
        bearing_rad = math.atan2(y, x)
        bearing_deg = math.degrees(bearing_rad)
        
        # Normalize to 0-360 degrees
        return (bearing_deg + 360) % 360
        
    def get_status(self) -> Dict:
        """Get autopilot status"""
        return {
            'active': self.is_active,
            'mode': self.settings.mode.value,
            'target_heading': self.settings.target_heading,
            'current_heading': self.navigation_data.heading,
            'last_correction': self.last_correction,
            'target_position': {
                'latitude': self.settings.target_latitude,
                'longitude': self.settings.target_longitude
            },
            'current_position': {
                'latitude': self.navigation_data.latitude,
                'longitude': self.navigation_data.longitude
            }
        }
