"""
TridentOS Engine Module
Engine monitoring and control logic
"""

import logging
from typing import Dict, Optional
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)


class EngineState(Enum):
    """Engine operating states"""
    OFF = "off"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class EngineData:
    """Engine sensor data"""
    temperature: float = 0.0
    oil_pressure: float = 0.0
    rpm: int = 0
    fuel_flow: float = 0.0
    hours: float = 0.0
    voltage: float = 0.0


@dataclass
class EngineThresholds:
    """Engine warning and critical thresholds"""
    temp_warning: float = 85.0
    temp_critical: float = 95.0
    oil_pressure_warning: float = 20.0
    oil_pressure_critical: float = 15.0
    rpm_max: int = 3500
    voltage_min: float = 11.0


class EngineModule:
    """Engine control and monitoring module"""
    
    def __init__(self):
        self.state = EngineState.OFF
        self.data = EngineData()
        self.thresholds = EngineThresholds()
        self.alarms = []
        self.start_time = None
        self.total_hours = 0.0
        
        logger.info("Engine module initialized")
        
    def start_engine(self) -> bool:
        """Start the engine"""
        try:
            if self.state != EngineState.OFF:
                logger.warning(f"Cannot start engine: current state is {self.state.value}")
                return False
                
            # Pre-start checks
            if not self._pre_start_checks():
                logger.error("Pre-start checks failed")
                return False
                
            self.state = EngineState.STARTING
            self.start_time = time.time()
            logger.info("Engine starting...")
            
            # In real implementation, this would trigger hardware
            # For now, simulate successful start after delay
            return True
            
        except Exception as e:
            logger.error(f"Failed to start engine: {e}")
            self.state = EngineState.ERROR
            return False
            
    def stop_engine(self) -> bool:
        """Stop the engine"""
        try:
            if self.state not in [EngineState.RUNNING, EngineState.STARTING]:
                logger.warning(f"Cannot stop engine: current state is {self.state.value}")
                return False
                
            self.state = EngineState.STOPPING
            logger.info("Engine stopping...")
            
            # Update total hours
            if self.start_time:
                session_hours = (time.time() - self.start_time) / 3600
                self.total_hours += session_hours
                self.start_time = None
                
            # In real implementation, this would trigger hardware
            self.state = EngineState.OFF
            self.data.rpm = 0
            
            logger.info("Engine stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop engine: {e}")
            return False
            
    def emergency_stop(self) -> bool:
        """Emergency engine stop"""
        try:
            logger.warning("EMERGENCY ENGINE STOP")
            self.state = EngineState.OFF
            self.data.rpm = 0
            
            if self.start_time:
                session_hours = (time.time() - self.start_time) / 3600
                self.total_hours += session_hours
                self.start_time = None
                
            return True
            
        except Exception as e:
            logger.error(f"Emergency stop failed: {e}")
            return False
            
    def update_sensor_data(self, sensor_data: Dict) -> None:
        """Update engine sensor data"""
        try:
            self.data.temperature = sensor_data.get('temperature', 0.0)
            self.data.oil_pressure = sensor_data.get('oil_pressure', 0.0)
            self.data.rpm = sensor_data.get('rpm', 0)
            self.data.fuel_flow = sensor_data.get('fuel_flow', 0.0)
            self.data.voltage = sensor_data.get('voltage', 0.0)
            
            # Update running hours
            if self.start_time and self.state == EngineState.RUNNING:
                session_hours = (time.time() - self.start_time) / 3600
                self.data.hours = self.total_hours + session_hours
                
            # Check for alarms
            self._check_alarms()
            
            # Auto-transition from starting to running
            if (self.state == EngineState.STARTING and 
                self.data.rpm > 500 and 
                self.data.oil_pressure > 10):
                self.state = EngineState.RUNNING
                logger.info("Engine started successfully")
                
        except Exception as e:
            logger.error(f"Failed to update sensor data: {e}")
            
    def _pre_start_checks(self) -> bool:
        """Perform pre-start safety checks"""
        checks = []
        
        # Check oil pressure (should be zero when off)
        if self.data.oil_pressure > 5:
            checks.append("Oil pressure sensor error")
            
        # Check temperature (should be reasonable)
        if self.data.temperature > 50:
            checks.append("Engine overheated")
            
        # Check voltage
        if self.data.voltage < self.thresholds.voltage_min:
            checks.append("Low battery voltage")
            
        if checks:
            logger.error(f"Pre-start check failures: {', '.join(checks)}")
            return False
            
        return True
        
    def _check_alarms(self) -> None:
        """Check for alarm conditions"""
        new_alarms = []
        
        # Temperature alarms
        if self.data.temperature >= self.thresholds.temp_critical:
            new_alarms.append({
                'type': 'CRITICAL',
                'message': f'Engine temperature critical: {self.data.temperature}°C',
                'auto_action': 'emergency_stop'
            })
        elif self.data.temperature >= self.thresholds.temp_warning:
            new_alarms.append({
                'type': 'WARNING',
                'message': f'Engine temperature high: {self.data.temperature}°C'
            })
            
        # Oil pressure alarms
        if (self.state == EngineState.RUNNING and 
            self.data.oil_pressure <= self.thresholds.oil_pressure_critical):
            new_alarms.append({
                'type': 'CRITICAL',
                'message': f'Oil pressure critical: {self.data.oil_pressure} PSI',
                'auto_action': 'emergency_stop'
            })
        elif (self.state == EngineState.RUNNING and 
              self.data.oil_pressure <= self.thresholds.oil_pressure_warning):
            new_alarms.append({
                'type': 'WARNING',
                'message': f'Oil pressure low: {self.data.oil_pressure} PSI'
            })
            
        # RPM alarms
        if self.data.rpm > self.thresholds.rpm_max:
            new_alarms.append({
                'type': 'WARNING',
                'message': f'Engine RPM high: {self.data.rpm}'
            })
            
        # Process new alarms
        for alarm in new_alarms:
            if alarm not in self.alarms:
                self.alarms.append(alarm)
                logger.warning(f"Engine alarm: {alarm['message']}")
                
                # Execute auto actions
                if alarm.get('auto_action') == 'emergency_stop':
                    self.emergency_stop()
                    
        # Remove resolved alarms
        self.alarms = [alarm for alarm in self.alarms 
                      if self._is_alarm_active(alarm)]
                      
    def _is_alarm_active(self, alarm: Dict) -> bool:
        """Check if an alarm condition is still active"""
        message = alarm['message']
        
        if 'temperature critical' in message:
            return self.data.temperature >= self.thresholds.temp_critical
        elif 'temperature high' in message:
            return self.data.temperature >= self.thresholds.temp_warning
        elif 'oil pressure critical' in message:
            return (self.state == EngineState.RUNNING and 
                   self.data.oil_pressure <= self.thresholds.oil_pressure_critical)
        elif 'oil pressure low' in message:
            return (self.state == EngineState.RUNNING and 
                   self.data.oil_pressure <= self.thresholds.oil_pressure_warning)
        elif 'RPM high' in message:
            return self.data.rpm > self.thresholds.rpm_max
            
        return False
        
    def get_status(self) -> Dict:
        """Get engine status"""
        return {
            'state': self.state.value,
            'data': {
                'temperature': self.data.temperature,
                'oil_pressure': self.data.oil_pressure,
                'rpm': self.data.rpm,
                'fuel_flow': self.data.fuel_flow,
                'hours': self.data.hours,
                'voltage': self.data.voltage
            },
            'alarms': self.alarms,
            'running': self.state == EngineState.RUNNING
        }
