"""
TridentOS Data Service
Central data management and distribution service
"""

import logging
import threading
import time
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class SensorReading:
    """Individual sensor reading"""
    timestamp: datetime
    sensor_id: str
    value: Any
    unit: str = ""
    quality: float = 1.0  # 0.0 to 1.0, 1.0 = perfect


@dataclass
class SystemData:
    """Complete system data snapshot"""
    timestamp: datetime
    engine: Dict = None
    battery: Dict = None
    fuel: Dict = None
    water: Dict = None
    navigation: Dict = None
    climate: Dict = None
    lighting: Dict = None
    emergency: Dict = None


class DataService:
    """Central data service for TridentOS"""
    
    def __init__(self):
        self.current_data = SystemData(timestamp=datetime.now())
        self.sensor_history = {}
        self.subscribers = {}
        self.data_lock = threading.RLock()
        self.running = False
        self.update_thread = None
        
        # Initialize default data
        self._initialize_default_data()
        
        logger.info("Data service initialized")
        
    def _initialize_default_data(self):
        """Initialize with default/mock data"""
        with self.data_lock:
            self.current_data.engine = {
                'running': False,
                'temperature': 75.0,
                'oil_pressure': 0.0,
                'rpm': 0,
                'fuel_flow': 0.0,
                'hours': 1234.5,
                'voltage': 12.4
            }
            
            self.current_data.battery = {
                'level': 85,
                'voltage': 12.4,
                'current': 0.0,
                'charging': False,
                'time_remaining': 480  # minutes
            }
            
            self.current_data.fuel = {
                'level': 75,
                'capacity': 200,  # liters
                'consumption_rate': 0.0,
                'range': 0  # nautical miles
            }
            
            self.current_data.water = {
                'fresh_water_level': 60,
                'fresh_water_capacity': 150,  # liters
                'bilge_level': 5,
                'bilge_pump_running': False
            }
            
            self.current_data.navigation = {
                'latitude': 0.0,
                'longitude': 0.0,
                'heading': 180.0,
                'speed': 0.0,
                'course': 180.0,
                'wind_direction': 270.0,
                'wind_speed': 5.0
            }
            
            self.current_data.climate = {
                'cabin_temperature': 22.0,
                'cabin_humidity': 55.0,
                'outside_temperature': 18.0,
                'outside_humidity': 70.0,
                'hvac_running': False
            }
            
            self.current_data.lighting = {
                'navigation_lights': False,
                'cabin_lights': False,
                'deck_lights': False,
                'emergency_lights': False
            }
            
            self.current_data.emergency = {
                'alarms': [],
                'emergency_beacon': False,
                'bilge_alarm': False,
                'fire_alarm': False
            }
            
    def start(self):
        """Start the data service"""
        if not self.running:
            self.running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            logger.info("Data service started")
            
    def stop(self):
        """Stop the data service"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("Data service stopped")
        
    def _update_loop(self):
        """Main update loop"""
        while self.running:
            try:
                self._update_data()
                self._notify_subscribers()
                time.sleep(1.0)  # Update every second
            except Exception as e:
                logger.error(f"Data service update error: {e}")
                time.sleep(5.0)  # Wait longer on error
                
    def _update_data(self):
        """Update system data from various sources"""
        with self.data_lock:
            self.current_data.timestamp = datetime.now()
            
            # In a real implementation, this would read from actual sensors
            # For now, simulate some changing values
            import random
            
            # Simulate battery drain
            if not self.current_data.battery['charging']:
                self.current_data.battery['level'] = max(0, 
                    self.current_data.battery['level'] - random.uniform(0, 0.01))
                    
            # Simulate wind changes
            self.current_data.navigation['wind_direction'] += random.uniform(-2, 2)
            self.current_data.navigation['wind_direction'] %= 360
            
            self.current_data.navigation['wind_speed'] = max(0,
                self.current_data.navigation['wind_speed'] + random.uniform(-0.5, 0.5))
                
    def _notify_subscribers(self):
        """Notify all data subscribers"""
        for callback in self.subscribers.values():
            try:
                callback(self.get_all_data())
            except Exception as e:
                logger.error(f"Error notifying subscriber: {e}")
                
    def subscribe(self, subscriber_id: str, callback: Callable) -> bool:
        """Subscribe to data updates"""
        try:
            self.subscribers[subscriber_id] = callback
            logger.debug(f"Subscriber {subscriber_id} added")
            return True
        except Exception as e:
            logger.error(f"Failed to add subscriber {subscriber_id}: {e}")
            return False
            
    def unsubscribe(self, subscriber_id: str) -> bool:
        """Unsubscribe from data updates"""
        try:
            if subscriber_id in self.subscribers:
                del self.subscribers[subscriber_id]
                logger.debug(f"Subscriber {subscriber_id} removed")
            return True
        except Exception as e:
            logger.error(f"Failed to remove subscriber {subscriber_id}: {e}")
            return False
            
    def update_sensor_data(self, sensor_id: str, value: Any, unit: str = "", 
                          quality: float = 1.0) -> bool:
        """Update individual sensor data"""
        try:
            reading = SensorReading(
                timestamp=datetime.now(),
                sensor_id=sensor_id,
                value=value,
                unit=unit,
                quality=quality
            )
            
            # Store in history
            if sensor_id not in self.sensor_history:
                self.sensor_history[sensor_id] = []
                
            self.sensor_history[sensor_id].append(reading)
            
            # Keep only last 1000 readings per sensor
            if len(self.sensor_history[sensor_id]) > 1000:
                self.sensor_history[sensor_id] = self.sensor_history[sensor_id][-1000:]
                
            # Update current data based on sensor ID
            self._update_current_data_from_sensor(sensor_id, value)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update sensor {sensor_id}: {e}")
            return False
            
    def _update_current_data_from_sensor(self, sensor_id: str, value: Any):
        """Update current system data from sensor reading"""
        with self.data_lock:
            # Map sensor IDs to data fields
            sensor_mapping = {
                'engine_temp': ('engine', 'temperature'),
                'engine_oil_pressure': ('engine', 'oil_pressure'),
                'engine_rpm': ('engine', 'rpm'),
                'battery_voltage': ('battery', 'voltage'),
                'battery_level': ('battery', 'level'),
                'fuel_level': ('fuel', 'level'),
                'water_level': ('water', 'fresh_water_level'),
                'cabin_temp': ('climate', 'cabin_temperature'),
                'gps_lat': ('navigation', 'latitude'),
                'gps_lon': ('navigation', 'longitude'),
                'compass_heading': ('navigation', 'heading'),
                'speed': ('navigation', 'speed'),
            }
            
            if sensor_id in sensor_mapping:
                system, field = sensor_mapping[sensor_id]
                if hasattr(self.current_data, system):
                    system_data = getattr(self.current_data, system)
                    if system_data and field in system_data:
                        system_data[field] = value
                        
    def get_dashboard_data(self) -> Dict:
        """Get data specifically formatted for dashboard"""
        with self.data_lock:
            return {
                'engine': self.current_data.engine,
                'battery': self.current_data.battery,
                'fuel': self.current_data.fuel,
                'water': self.current_data.water,
                'navigation': self.current_data.navigation
            }
            
    def get_system_data(self, system: str) -> Optional[Dict]:
        """Get data for a specific system"""
        with self.data_lock:
            if hasattr(self.current_data, system):
                return getattr(self.current_data, system)
            return None
            
    def get_all_data(self) -> Dict:
        """Get all current system data"""
        with self.data_lock:
            return asdict(self.current_data)
            
    def get_sensor_history(self, sensor_id: str, limit: int = 100) -> List[SensorReading]:
        """Get historical data for a sensor"""
        if sensor_id in self.sensor_history:
            return self.sensor_history[sensor_id][-limit:]
        return []
        
    def export_data(self, start_time: datetime = None, end_time: datetime = None) -> str:
        """Export data to JSON format"""
        try:
            export_data = {
                'current_data': asdict(self.current_data),
                'sensor_history': {}
            }
            
            # Export sensor history within time range
            for sensor_id, readings in self.sensor_history.items():
                filtered_readings = []
                for reading in readings:
                    if start_time and reading.timestamp < start_time:
                        continue
                    if end_time and reading.timestamp > end_time:
                        continue
                    filtered_readings.append(asdict(reading))
                    
                if filtered_readings:
                    export_data['sensor_history'][sensor_id] = filtered_readings
                    
            return json.dumps(export_data, default=str, indent=2)
            
        except Exception as e:
            logger.error(f"Failed to export data: {e}")
            return "{}"
