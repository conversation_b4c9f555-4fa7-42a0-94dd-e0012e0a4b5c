"""
TridentOS System Monitor Service
Monitors system health, resources, and performance
"""

import logging
import threading
import time
import psutil
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    temperature: float
    uptime: float
    load_average: List[float]


@dataclass
class ProcessInfo:
    """Process information"""
    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    status: str


class SystemMonitor:
    """System monitoring service"""
    
    def __init__(self, update_interval: float = 5.0):
        self.update_interval = update_interval
        self.running = False
        self.monitor_thread = None
        self.metrics_history = []
        self.max_history = 1440  # 24 hours at 1-minute intervals
        self.callbacks = {}
        
        # System thresholds
        self.thresholds = {
            'cpu_warning': 80.0,
            'cpu_critical': 95.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'temp_warning': 70.0,
            'temp_critical': 80.0
        }
        
        self.alerts = []
        
        logger.info("System monitor initialized")
        
    def start(self):
        """Start system monitoring"""
        if not self.running:
            self.running = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("System monitor started")
            
    def stop(self):
        """Stop system monitoring"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        logger.info("System monitor stopped")
        
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                metrics = self._collect_metrics()
                self._store_metrics(metrics)
                self._check_thresholds(metrics)
                self._notify_callbacks(metrics)
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"System monitor error: {e}")
                time.sleep(self.update_interval * 2)
                
    def _collect_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage (root filesystem)
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # System temperature (if available)
            temperature = self._get_cpu_temperature()
            
            # System uptime
            boot_time = psutil.boot_time()
            uptime = time.time() - boot_time
            
            # Load average
            try:
                load_avg = psutil.getloadavg()
            except AttributeError:
                # getloadavg not available on Windows
                load_avg = [0.0, 0.0, 0.0]
                
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                temperature=temperature,
                uptime=uptime,
                load_average=list(load_avg)
            )
            
        except Exception as e:
            logger.error(f"Failed to collect metrics: {e}")
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0,
                temperature=0.0,
                uptime=0.0,
                load_average=[0.0, 0.0, 0.0]
            )
            
    def _get_cpu_temperature(self) -> float:
        """Get CPU temperature (Raspberry Pi specific)"""
        try:
            # Try to read from Raspberry Pi thermal zone
            with open('/sys/class/thermal/thermal_zone0/temp', 'r') as f:
                temp_str = f.read().strip()
                return float(temp_str) / 1000.0  # Convert from millidegrees
        except (FileNotFoundError, ValueError, PermissionError):
            # Fallback: try psutil sensors
            try:
                temps = psutil.sensors_temperatures()
                if 'cpu_thermal' in temps:
                    return temps['cpu_thermal'][0].current
                elif temps:
                    # Return first available temperature
                    return list(temps.values())[0][0].current
            except (AttributeError, IndexError, KeyError):
                pass
                
        return 0.0  # No temperature available
        
    def _store_metrics(self, metrics: SystemMetrics):
        """Store metrics in history"""
        self.metrics_history.append(metrics)
        
        # Limit history size
        if len(self.metrics_history) > self.max_history:
            self.metrics_history = self.metrics_history[-self.max_history:]
            
    def _check_thresholds(self, metrics: SystemMetrics):
        """Check metrics against thresholds and generate alerts"""
        new_alerts = []
        
        # CPU threshold check
        if metrics.cpu_percent >= self.thresholds['cpu_critical']:
            new_alerts.append({
                'type': 'CRITICAL',
                'component': 'CPU',
                'message': f'CPU usage critical: {metrics.cpu_percent:.1f}%',
                'timestamp': metrics.timestamp,
                'value': metrics.cpu_percent
            })
        elif metrics.cpu_percent >= self.thresholds['cpu_warning']:
            new_alerts.append({
                'type': 'WARNING',
                'component': 'CPU',
                'message': f'CPU usage high: {metrics.cpu_percent:.1f}%',
                'timestamp': metrics.timestamp,
                'value': metrics.cpu_percent
            })
            
        # Memory threshold check
        if metrics.memory_percent >= self.thresholds['memory_critical']:
            new_alerts.append({
                'type': 'CRITICAL',
                'component': 'Memory',
                'message': f'Memory usage critical: {metrics.memory_percent:.1f}%',
                'timestamp': metrics.timestamp,
                'value': metrics.memory_percent
            })
        elif metrics.memory_percent >= self.thresholds['memory_warning']:
            new_alerts.append({
                'type': 'WARNING',
                'component': 'Memory',
                'message': f'Memory usage high: {metrics.memory_percent:.1f}%',
                'timestamp': metrics.timestamp,
                'value': metrics.memory_percent
            })
            
        # Disk threshold check
        if metrics.disk_percent >= self.thresholds['disk_critical']:
            new_alerts.append({
                'type': 'CRITICAL',
                'component': 'Disk',
                'message': f'Disk usage critical: {metrics.disk_percent:.1f}%',
                'timestamp': metrics.timestamp,
                'value': metrics.disk_percent
            })
        elif metrics.disk_percent >= self.thresholds['disk_warning']:
            new_alerts.append({
                'type': 'WARNING',
                'component': 'Disk',
                'message': f'Disk usage high: {metrics.disk_percent:.1f}%',
                'timestamp': metrics.timestamp,
                'value': metrics.disk_percent
            })
            
        # Temperature threshold check
        if metrics.temperature > 0:  # Only check if temperature is available
            if metrics.temperature >= self.thresholds['temp_critical']:
                new_alerts.append({
                    'type': 'CRITICAL',
                    'component': 'Temperature',
                    'message': f'CPU temperature critical: {metrics.temperature:.1f}°C',
                    'timestamp': metrics.timestamp,
                    'value': metrics.temperature
                })
            elif metrics.temperature >= self.thresholds['temp_warning']:
                new_alerts.append({
                    'type': 'WARNING',
                    'component': 'Temperature',
                    'message': f'CPU temperature high: {metrics.temperature:.1f}°C',
                    'timestamp': metrics.timestamp,
                    'value': metrics.temperature
                })
                
        # Add new alerts and log them
        for alert in new_alerts:
            if not self._alert_exists(alert):
                self.alerts.append(alert)
                if alert['type'] == 'CRITICAL':
                    logger.critical(alert['message'])
                else:
                    logger.warning(alert['message'])
                    
        # Clean up old alerts (older than 1 hour)
        cutoff_time = datetime.now() - timedelta(hours=1)
        self.alerts = [alert for alert in self.alerts 
                      if alert['timestamp'] > cutoff_time]
                      
    def _alert_exists(self, new_alert: Dict) -> bool:
        """Check if similar alert already exists"""
        for existing_alert in self.alerts:
            if (existing_alert['component'] == new_alert['component'] and
                existing_alert['type'] == new_alert['type'] and
                (new_alert['timestamp'] - existing_alert['timestamp']).seconds < 300):  # 5 minutes
                return True
        return False
        
    def _notify_callbacks(self, metrics: SystemMetrics):
        """Notify registered callbacks"""
        for callback in self.callbacks.values():
            try:
                callback(metrics)
            except Exception as e:
                logger.error(f"Error in system monitor callback: {e}")
                
    def subscribe(self, callback_id: str, callback) -> bool:
        """Subscribe to system metrics updates"""
        try:
            self.callbacks[callback_id] = callback
            return True
        except Exception as e:
            logger.error(f"Failed to subscribe callback {callback_id}: {e}")
            return False
            
    def unsubscribe(self, callback_id: str) -> bool:
        """Unsubscribe from system metrics updates"""
        try:
            if callback_id in self.callbacks:
                del self.callbacks[callback_id]
            return True
        except Exception as e:
            logger.error(f"Failed to unsubscribe callback {callback_id}: {e}")
            return False
            
    def get_current_metrics(self) -> Optional[SystemMetrics]:
        """Get the most recent metrics"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
        
    def get_metrics_history(self, hours: int = 1) -> List[SystemMetrics]:
        """Get metrics history for specified hours"""
        if not self.metrics_history:
            return []
            
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [m for m in self.metrics_history if m.timestamp > cutoff_time]
        
    def get_active_alerts(self) -> List[Dict]:
        """Get current active alerts"""
        return self.alerts.copy()
        
    def get_system_info(self) -> Dict:
        """Get general system information"""
        try:
            return {
                'platform': psutil.LINUX if hasattr(psutil, 'LINUX') else 'unknown',
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'disk_total': psutil.disk_usage('/').total,
                'boot_time': datetime.fromtimestamp(psutil.boot_time()),
                'python_version': psutil.version_info if hasattr(psutil, 'version_info') else 'unknown'
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {e}")
            return {}
            
    def get_top_processes(self, limit: int = 10) -> List[ProcessInfo]:
        """Get top processes by CPU usage"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(ProcessInfo(
                        pid=proc.info['pid'],
                        name=proc.info['name'],
                        cpu_percent=proc.info['cpu_percent'] or 0.0,
                        memory_percent=proc.info['memory_percent'] or 0.0,
                        status=proc.info['status']
                    ))
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            # Sort by CPU usage and return top processes
            processes.sort(key=lambda p: p.cpu_percent, reverse=True)
            return processes[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get top processes: {e}")
            return []
