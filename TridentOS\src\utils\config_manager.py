"""
TridentOS Configuration Manager
Centralized configuration management for system settings
"""

import os
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
import threading

logger = logging.getLogger(__name__)


@dataclass
class ConfigSection:
    """Base configuration section"""
    pass


@dataclass
class SystemConfig(ConfigSection):
    """System configuration"""
    name: str = "TridentOS"
    version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"


@dataclass
class DisplayConfig(ConfigSection):
    """Display configuration"""
    width: int = 1920
    height: int = 1080
    fullscreen: bool = True
    show_cursor: bool = False
    orientation: str = "landscape"
    brightness: int = 80


@dataclass
class HardwareConfig(ConfigSection):
    """Hardware configuration"""
    gpio_pins: Dict[str, int] = None
    uart: Dict[str, Any] = None
    i2c: Dict[str, Any] = None
    spi: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.gpio_pins is None:
            self.gpio_pins = {}
        if self.uart is None:
            self.uart = {}
        if self.i2c is None:
            self.i2c = {}
        if self.spi is None:
            self.spi = {}


class ConfigManager:
    """Configuration manager for TridentOS"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_lock = threading.RLock()
        self.configs = {}
        self.watchers = {}
        
        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)
        
        # Load all configurations
        self._load_all_configs()
        
        logger.info(f"Configuration manager initialized - Config dir: {self.config_dir}")
        
    def _load_all_configs(self):
        """Load all configuration files"""
        config_files = {
            'system': 'system.yaml',
            'network': 'network.yaml',
            'hardware': 'hardware.yaml',
            'thresholds': 'thresholds.yaml'
        }
        
        for config_name, filename in config_files.items():
            self.load_config(config_name, filename)
            
    def load_config(self, config_name: str, filename: str) -> bool:
        """
        Load configuration from file
        
        Args:
            config_name: Name to store config under
            filename: Configuration filename
            
        Returns:
            True if successful, False otherwise
        """
        try:
            config_path = self.config_dir / filename
            
            if not config_path.exists():
                logger.warning(f"Config file not found: {config_path}")
                self.configs[config_name] = {}
                return False
                
            with self.config_lock:
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f) or {}
                elif filename.endswith('.json'):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                else:
                    logger.error(f"Unsupported config file format: {filename}")
                    return False
                    
                self.configs[config_name] = config_data
                logger.info(f"Loaded configuration: {config_name} from {filename}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to load config {config_name}: {e}")
            self.configs[config_name] = {}
            return False
            
    def save_config(self, config_name: str, filename: str = None) -> bool:
        """
        Save configuration to file
        
        Args:
            config_name: Name of config to save
            filename: Optional filename override
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if config_name not in self.configs:
                logger.error(f"Config not found: {config_name}")
                return False
                
            if filename is None:
                filename = f"{config_name}.yaml"
                
            config_path = self.config_dir / filename
            
            with self.config_lock:
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    with open(config_path, 'w', encoding='utf-8') as f:
                        yaml.dump(self.configs[config_name], f, 
                                default_flow_style=False, indent=2)
                elif filename.endswith('.json'):
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(self.configs[config_name], f, indent=2)
                else:
                    logger.error(f"Unsupported config file format: {filename}")
                    return False
                    
                logger.info(f"Saved configuration: {config_name} to {filename}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save config {config_name}: {e}")
            return False
            
    def get_config(self, config_name: str) -> Dict[str, Any]:
        """
        Get configuration by name
        
        Args:
            config_name: Name of configuration
            
        Returns:
            Configuration dictionary
        """
        with self.config_lock:
            return self.configs.get(config_name, {}).copy()
            
    def get_value(self, config_name: str, key_path: str, default: Any = None) -> Any:
        """
        Get specific configuration value using dot notation
        
        Args:
            config_name: Name of configuration
            key_path: Dot-separated path to value (e.g., 'system.debug')
            default: Default value if not found
            
        Returns:
            Configuration value or default
        """
        try:
            with self.config_lock:
                config = self.configs.get(config_name, {})
                
                # Navigate through nested keys
                keys = key_path.split('.')
                value = config
                
                for key in keys:
                    if isinstance(value, dict) and key in value:
                        value = value[key]
                    else:
                        return default
                        
                return value
                
        except Exception as e:
            logger.error(f"Failed to get config value {config_name}.{key_path}: {e}")
            return default
            
    def set_value(self, config_name: str, key_path: str, value: Any) -> bool:
        """
        Set specific configuration value using dot notation
        
        Args:
            config_name: Name of configuration
            key_path: Dot-separated path to value
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.config_lock:
                if config_name not in self.configs:
                    self.configs[config_name] = {}
                    
                config = self.configs[config_name]
                keys = key_path.split('.')
                
                # Navigate to parent of target key
                current = config
                for key in keys[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                    
                # Set the value
                current[keys[-1]] = value
                
                logger.debug(f"Set config value {config_name}.{key_path} = {value}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to set config value {config_name}.{key_path}: {e}")
            return False
            
    def update_config(self, config_name: str, updates: Dict[str, Any]) -> bool:
        """
        Update configuration with new values
        
        Args:
            config_name: Name of configuration
            updates: Dictionary of updates to apply
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.config_lock:
                if config_name not in self.configs:
                    self.configs[config_name] = {}
                    
                self._deep_update(self.configs[config_name], updates)
                logger.info(f"Updated configuration: {config_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update config {config_name}: {e}")
            return False
            
    def _deep_update(self, target: Dict, source: Dict):
        """Recursively update nested dictionaries"""
        for key, value in source.items():
            if (key in target and 
                isinstance(target[key], dict) and 
                isinstance(value, dict)):
                self._deep_update(target[key], value)
            else:
                target[key] = value
                
    def reload_config(self, config_name: str) -> bool:
        """
        Reload configuration from file
        
        Args:
            config_name: Name of configuration to reload
            
        Returns:
            True if successful, False otherwise
        """
        filename = f"{config_name}.yaml"
        return self.load_config(config_name, filename)
        
    def get_system_config(self) -> SystemConfig:
        """Get system configuration as dataclass"""
        config = self.get_config('system')
        system_data = config.get('system', {})
        
        return SystemConfig(
            name=system_data.get('name', 'TridentOS'),
            version=system_data.get('version', '1.0.0'),
            debug=system_data.get('debug', False),
            log_level=system_data.get('log_level', 'INFO')
        )
        
    def get_display_config(self) -> DisplayConfig:
        """Get display configuration as dataclass"""
        config = self.get_config('system')
        display_data = config.get('display', {})
        
        return DisplayConfig(
            width=display_data.get('width', 1920),
            height=display_data.get('height', 1080),
            fullscreen=display_data.get('fullscreen', True),
            show_cursor=display_data.get('show_cursor', False),
            orientation=display_data.get('orientation', 'landscape'),
            brightness=display_data.get('brightness', 80)
        )
        
    def get_hardware_config(self) -> HardwareConfig:
        """Get hardware configuration as dataclass"""
        config = self.get_config('system')
        hardware_data = config.get('hardware', {})
        
        return HardwareConfig(
            gpio_pins=hardware_data.get('gpio_pins', {}),
            uart=hardware_data.get('uart', {}),
            i2c=hardware_data.get('i2c', {}),
            spi=hardware_data.get('spi', {})
        )
        
    def validate_config(self, config_name: str, schema: Dict = None) -> bool:
        """
        Validate configuration against schema
        
        Args:
            config_name: Name of configuration to validate
            schema: Optional validation schema
            
        Returns:
            True if valid, False otherwise
        """
        try:
            config = self.get_config(config_name)
            
            if schema is None:
                # Basic validation - check if config exists and is not empty
                return bool(config)
                
            # TODO: Implement schema validation using jsonschema
            logger.warning("Schema validation not implemented yet")
            return True
            
        except Exception as e:
            logger.error(f"Config validation failed for {config_name}: {e}")
            return False
            
    def export_config(self, config_name: str = None) -> str:
        """
        Export configuration(s) to JSON string
        
        Args:
            config_name: Specific config to export, or None for all
            
        Returns:
            JSON string of configuration(s)
        """
        try:
            with self.config_lock:
                if config_name:
                    data = {config_name: self.configs.get(config_name, {})}
                else:
                    data = self.configs.copy()
                    
                return json.dumps(data, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Failed to export config: {e}")
            return "{}"
            
    def import_config(self, json_data: str) -> bool:
        """
        Import configuration(s) from JSON string
        
        Args:
            json_data: JSON string containing configuration data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            data = json.loads(json_data)
            
            with self.config_lock:
                for config_name, config_data in data.items():
                    self.configs[config_name] = config_data
                    logger.info(f"Imported configuration: {config_name}")
                    
            return True
            
        except Exception as e:
            logger.error(f"Failed to import config: {e}")
            return False
