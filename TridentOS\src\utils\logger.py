"""
TridentOS Logging Utilities
Centralized logging configuration and utilities
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional


def setup_logging(log_level: str = "INFO", 
                 log_file: Optional[str] = None,
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 console_output: bool = True) -> None:
    """
    Setup centralized logging for TridentOS
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (default: logs/tridentos.log)
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
        console_output: Whether to output logs to console
    """
    
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Default log file
    if log_file is None:
        log_file = log_dir / "tridentos.log"
    else:
        log_file = Path(log_file)
        
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
    # Set specific logger levels
    logging.getLogger('kivy').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(f"TridentOS logging initialized - Level: {log_level}")
    logger.info(f"Log file: {log_file}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class TridentOSLogger:
    """
    Custom logger class with TridentOS-specific functionality
    """
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.component = name.split('.')[-1] if '.' in name else name
        
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(f"[{self.component}] {message}", **kwargs)
        
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(f"[{self.component}] {message}", **kwargs)
        
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(f"[{self.component}] {message}", **kwargs)
        
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(f"[{self.component}] {message}", **kwargs)
        
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self.logger.critical(f"[{self.component}] {message}", **kwargs)
        
    def exception(self, message: str, **kwargs):
        """Log exception with traceback"""
        self.logger.exception(f"[{self.component}] {message}", **kwargs)
        
    def system_event(self, event: str, details: str = ""):
        """Log system event"""
        message = f"SYSTEM EVENT: {event}"
        if details:
            message += f" - {details}"
        self.info(message)
        
    def user_action(self, action: str, user: str = "unknown"):
        """Log user action"""
        self.info(f"USER ACTION: {user} - {action}")
        
    def hardware_event(self, device: str, event: str, value=None):
        """Log hardware event"""
        message = f"HARDWARE: {device} - {event}"
        if value is not None:
            message += f" ({value})"
        self.info(message)
        
    def alarm(self, alarm_type: str, message: str, severity: str = "WARNING"):
        """Log alarm event"""
        log_method = getattr(self.logger, severity.lower(), self.logger.warning)
        log_method(f"ALARM [{alarm_type}]: {message}")


def create_audit_logger(log_file: str = "logs/audit.log") -> logging.Logger:
    """
    Create a separate audit logger for security and compliance events
    
    Args:
        log_file: Path to audit log file
        
    Returns:
        Audit logger instance
    """
    audit_logger = logging.getLogger('tridentos.audit')
    audit_logger.setLevel(logging.INFO)
    
    # Create audit log directory
    log_path = Path(log_file)
    log_path.parent.mkdir(exist_ok=True)
    
    # Audit log handler (no rotation for compliance)
    audit_handler = logging.FileHandler(log_file, encoding='utf-8')
    audit_formatter = logging.Formatter(
        fmt='%(asctime)s - AUDIT - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    audit_handler.setFormatter(audit_formatter)
    audit_logger.addHandler(audit_handler)
    
    # Prevent propagation to root logger
    audit_logger.propagate = False
    
    return audit_logger


def log_performance(func):
    """
    Decorator to log function performance
    
    Usage:
        @log_performance
        def my_function():
            pass
    """
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"Performance: {func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Performance: {func.__name__} failed after {execution_time:.3f}s - {e}")
            raise
            
    return wrapper


def log_system_startup():
    """Log system startup information"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 50)
    logger.info("TridentOS System Starting")
    logger.info("=" * 50)
    logger.info(f"Timestamp: {datetime.now()}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Platform: {sys.platform}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    # Log system resources
    try:
        import psutil
        logger.info(f"CPU cores: {psutil.cpu_count()}")
        memory = psutil.virtual_memory()
        logger.info(f"Memory: {memory.total / (1024**3):.1f} GB total, "
                   f"{memory.available / (1024**3):.1f} GB available")
        disk = psutil.disk_usage('/')
        logger.info(f"Disk: {disk.total / (1024**3):.1f} GB total, "
                   f"{disk.free / (1024**3):.1f} GB free")
    except ImportError:
        logger.warning("psutil not available - system resource info not logged")
        
    logger.info("=" * 50)


def log_system_shutdown():
    """Log system shutdown information"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 50)
    logger.info("TridentOS System Shutting Down")
    logger.info(f"Timestamp: {datetime.now()}")
    logger.info("=" * 50)


# Create module-level logger
logger = get_logger(__name__)
