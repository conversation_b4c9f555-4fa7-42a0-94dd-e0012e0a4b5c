"""
Test suite for ConfigManager
"""

import pytest
import tempfile
import shutil
from pathlib import Path
import yaml
import json

from src.utils.config_manager import ConfigManager, SystemConfig, DisplayConfig


class TestConfigManager:
    """Test cases for ConfigManager"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.config_dir.mkdir()
        
        # Create test config files
        self.create_test_configs()
        
        self.config_manager = ConfigManager(str(self.config_dir))
        
    def teardown_method(self):
        """Cleanup test environment"""
        shutil.rmtree(self.temp_dir)
        
    def create_test_configs(self):
        """Create test configuration files"""
        # System config
        system_config = {
            'system': {
                'name': 'TestOS',
                'version': '1.0.0',
                'debug': True,
                'log_level': 'DEBUG'
            },
            'display': {
                'width': 1024,
                'height': 768,
                'fullscreen': False
            }
        }
        
        with open(self.config_dir / 'system.yaml', 'w') as f:
            yaml.dump(system_config, f)
            
        # Network config
        network_config = {
            'wifi': {
                'ssid': 'TestNetwork',
                'password': 'testpass'
            }
        }
        
        with open(self.config_dir / 'network.yaml', 'w') as f:
            yaml.dump(network_config, f)
            
    def test_load_config(self):
        """Test loading configuration"""
        config = self.config_manager.get_config('system')
        assert config['system']['name'] == 'TestOS'
        assert config['display']['width'] == 1024
        
    def test_get_value(self):
        """Test getting specific configuration values"""
        name = self.config_manager.get_value('system', 'system.name')
        assert name == 'TestOS'
        
        width = self.config_manager.get_value('system', 'display.width')
        assert width == 1024
        
        # Test default value
        missing = self.config_manager.get_value('system', 'missing.key', 'default')
        assert missing == 'default'
        
    def test_set_value(self):
        """Test setting configuration values"""
        success = self.config_manager.set_value('system', 'system.name', 'NewTestOS')
        assert success
        
        name = self.config_manager.get_value('system', 'system.name')
        assert name == 'NewTestOS'
        
    def test_update_config(self):
        """Test updating configuration"""
        updates = {
            'system': {
                'name': 'UpdatedOS',
                'new_field': 'new_value'
            }
        }
        
        success = self.config_manager.update_config('system', updates)
        assert success
        
        name = self.config_manager.get_value('system', 'system.name')
        assert name == 'UpdatedOS'
        
        new_field = self.config_manager.get_value('system', 'system.new_field')
        assert new_field == 'new_value'
        
    def test_save_config(self):
        """Test saving configuration"""
        self.config_manager.set_value('system', 'system.name', 'SavedOS')
        success = self.config_manager.save_config('system', 'test_save.yaml')
        assert success
        
        # Verify file was created
        saved_file = self.config_dir / 'test_save.yaml'
        assert saved_file.exists()
        
        # Verify content
        with open(saved_file, 'r') as f:
            saved_config = yaml.safe_load(f)
        assert saved_config['system']['name'] == 'SavedOS'
        
    def test_system_config_dataclass(self):
        """Test system configuration dataclass"""
        system_config = self.config_manager.get_system_config()
        assert isinstance(system_config, SystemConfig)
        assert system_config.name == 'TestOS'
        assert system_config.debug == True
        
    def test_display_config_dataclass(self):
        """Test display configuration dataclass"""
        display_config = self.config_manager.get_display_config()
        assert isinstance(display_config, DisplayConfig)
        assert display_config.width == 1024
        assert display_config.height == 768
        assert display_config.fullscreen == False
        
    def test_export_import_config(self):
        """Test configuration export and import"""
        # Export
        exported = self.config_manager.export_config('system')
        assert 'TestOS' in exported
        
        # Modify and import
        import_data = json.loads(exported)
        import_data['system']['system']['name'] = 'ImportedOS'
        
        success = self.config_manager.import_config(json.dumps(import_data))
        assert success
        
        name = self.config_manager.get_value('system', 'system.name')
        assert name == 'ImportedOS'
        
    def test_missing_config_file(self):
        """Test handling of missing configuration file"""
        # Try to load non-existent config
        success = self.config_manager.load_config('missing', 'missing.yaml')
        assert not success
        
        # Should create empty config
        config = self.config_manager.get_config('missing')
        assert config == {}
        
    def test_invalid_config_file(self):
        """Test handling of invalid configuration file"""
        # Create invalid YAML file
        invalid_file = self.config_dir / 'invalid.yaml'
        with open(invalid_file, 'w') as f:
            f.write('invalid: yaml: content: [')
            
        success = self.config_manager.load_config('invalid', 'invalid.yaml')
        assert not success
        
        # Should create empty config on error
        config = self.config_manager.get_config('invalid')
        assert config == {}


if __name__ == '__main__':
    pytest.main([__file__])
