2025-10-01 08:28:44 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:28:44 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:28:44 - kivy - ERROR - KV load error: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 33:
...
     31:    height: self.minimum_height
     32:    canvas.before:
>>   33:        Color: rgba: COLOR_CARD
     34:        RoundedRectangle:
     35:            pos: self.pos
...
Invalid data after declaration
2025-10-01 08:28:44 - kivy - ERROR - Traceback (most recent call last):
  File "E:\PoseidonBox\Software\TridentOS\main.py", line 108, in build
    Builder.load_file(str(kv_path))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 310, in load_file
    return self.load_string(data, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 377, in load_string
    parser = Parser(content=string, filename=fn)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 483, in __init__
    self.parse(content)
    ~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 590, in parse
    self.execute_directives()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 543, in execute_directives
    kivy.lang.builder.Builder.load_file(ref)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 310, in load_file
    return self.load_string(data, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 377, in load_string
    parser = Parser(content=string, filename=fn)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 483, in __init__
    self.parse(content)
    ~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 593, in parse
    objects, remaining_lines = self.parse_level(0, lines)
                               ~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 741, in parse_level
    _objects, _lines = self.parse_level(
                       ~~~~~~~~~~~~~~~~^
        level + 2, lines[i:], spaces)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 666, in parse_level
    raise ParserException(self, ln,
                          'Invalid data after declaration')
kivy.lang.parser.ParserException: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 33:
...
     31:    height: self.minimum_height
     32:    canvas.before:
>>   33:        Color: rgba: COLOR_CARD
     34:        RoundedRectangle:
     35:            pos: self.pos
...
Invalid data after declaration

2025-10-01 08:28:44 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:28:44 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:28:44 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:30:35 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:30:35 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:30:35 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:30:35 - kivy - ERROR - KV load error: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\home.kv", line 10:
...
      8:        spacing: dp(12)
      9:        canvas.before:
>>   10:            Color: rgba: COLOR_BG
     11:            Rectangle:
     12:                pos: self.pos
...
Invalid data after declaration
2025-10-01 08:30:35 - kivy - ERROR - Traceback (most recent call last):
  File "E:\PoseidonBox\Software\TridentOS\main.py", line 108, in build
    Builder.load_file(str(kv_path))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 310, in load_file
    return self.load_string(data, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 377, in load_string
    parser = Parser(content=string, filename=fn)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 483, in __init__
    self.parse(content)
    ~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 593, in parse
    objects, remaining_lines = self.parse_level(0, lines)
                               ~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 696, in parse_level
    _objects, _lines = self.parse_level(
                       ~~~~~~~~~~~~~~~~^
        level + 1, lines[i:], spaces)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 741, in parse_level
    _objects, _lines = self.parse_level(
                       ~~~~~~~~~~~~~~~~^
        level + 2, lines[i:], spaces)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\parser.py", line 666, in parse_level
    raise ParserException(self, ln,
                          'Invalid data after declaration')
kivy.lang.parser.ParserException: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\home.kv", line 10:
...
      8:        spacing: dp(12)
      9:        canvas.before:
>>   10:            Color: rgba: COLOR_BG
     11:            Rectangle:
     12:                pos: self.pos
...
Invalid data after declaration

2025-10-01 08:30:35 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:30:35 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:30:35 - src.services.data_service - INFO - Data service initialized
2025-10-01 08:31:44 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:31:44 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:31:44 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:31:44 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:31:44 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:31:44 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000018C1CCE7690>" was accessed, it will be removed in a future version
2025-10-01 08:31:44 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000018C1CCE7690>" was accessed, it will be removed in a future version
2025-10-01 08:31:44 - kivy - ERROR - TridentOS: Failed to start application: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 70:
...
     68:    canvas.before:
     69:        Color:
>>   70:            rgba: self.bg_color
     71:        Line:
     72:            circle: (self.center_x, self.center_y, min(self.width, self.height)/2 - self.thickness/2)
...
TypeError: 'NoneType' object is not iterable
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 930, in _build_canvas
    setattr(instr, key, value)
    ~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "kivy\\graphics\\context_instructions.pyx", line 275, in kivy.graphics.context_instructions.Color.rgba.__set__

2025-10-01 08:32:05 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:32:05 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:32:05 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:32:05 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:32:05 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:32:05 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x00000278BDB475B0>" was accessed, it will be removed in a future version
2025-10-01 08:32:05 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x00000278BDB475B0>" was accessed, it will be removed in a future version
2025-10-01 08:32:05 - kivy - ERROR - TridentOS: Failed to start application: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 70:
...
     68:            rgba: rgba('#0C3B49')
     69:        Line:
>>   70:            circle: (self.center_x, self.center_y, min(self.width, self.height)/2 - self.thickness/2)
     71:            width: self.thickness
     72:        Color:
...
BuilderException: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 70:
...
     68:            rgba: rgba('#0C3B49')
     69:        Line:
>>   70:            circle: (self.center_x, self.center_y, min(self.width, self.height)/2 - self.thickness/2)
     71:            width: self.thickness
     72:        Color:
...
TypeError: unsupported operand type(s) for /: 'NoneType' and 'int'
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 245, in create_handler
    return eval(value, idmap), bound_list
           ~~~~^^^^^^^^^^^^^^
  File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 70, in <module>
    circle: (self.center_x, self.center_y, min(self.width, self.height)/2 - self.thickness/2)
                                                        ^^^^^^^^^^^^^^^^

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 927, in _build_canvas
    value, _ = create_handler(
               ~~~~~~~~~~~~~~^
        widget, instr.proxy_ref,
        ^^^^^^^^^^^^^^^^^^^^^^^^
        key, value, prule, idmap, True)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 248, in create_handler
    raise BuilderException(rule.ctx, rule.line,
                           '{}: {}'.format(e.__class__.__name__, e),
                           cause=tb)

2025-10-01 08:32:55 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-01 08:32:55 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-01 08:32:55 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-01 08:32:55 - src.services.system_monitor - INFO - System monitor initialized
2025-10-01 08:32:55 - src.services.system_monitor - INFO - System monitor started
2025-10-01 08:32:55 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x000001E45A8975B0>" was accessed, it will be removed in a future version
2025-10-01 08:32:55 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x000001E45A8975B0>" was accessed, it will be removed in a future version
2025-10-01 08:32:55 - kivy - ERROR - TridentOS: Failed to start application: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 70:
...
     68:            rgba: rgba('#0C3B49')
     69:        Line:
>>   70:            circle: (self.center_x, self.center_y, min(self.width, self.height)/2 - self.thickness/2)
     71:            width: self.thickness
     72:        Color:
...
BuilderException: Parser: File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 70:
...
     68:            rgba: rgba('#0C3B49')
     69:        Line:
>>   70:            circle: (self.center_x, self.center_y, min(self.width, self.height)/2 - self.thickness/2)
     71:            width: self.thickness
     72:        Color:
...
TypeError: unsupported operand type(s) for /: 'NoneType' and 'int'
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 245, in create_handler
    return eval(value, idmap), bound_list
           ~~~~^^^^^^^^^^^^^^
  File "E:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv", line 70, in <module>
    circle: (self.center_x, self.center_y, min(self.width, self.height)/2 - self.thickness/2)
                                                        ^^^^^^^^^^^^^^^^

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 927, in _build_canvas
    value, _ = create_handler(
               ~~~~~~~~~~~~~~^
        widget, instr.proxy_ref,
        ^^^^^^^^^^^^^^^^^^^^^^^^
        key, value, prule, idmap, True)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\kivy\lang\builder.py", line 248, in create_handler
    raise BuilderException(rule.ctx, rule.line,
                           '{}: {}'.format(e.__class__.__name__, e),
                           cause=tb)

2025-10-05 10:02:42 - src.utils.logger - INFO - TridentOS logging initialized - Level: INFO
2025-10-05 10:02:42 - src.utils.logger - INFO - Log file: logs\tridentos.log
2025-10-05 10:02:42 - kivy - WARNING - Factory: Ignored class "ActionButton" re-declaration. Current -  module: kivy.uix.actionbar, cls: None, baseclass: None, filename: None. Ignored -  module: None, cls: None, baseclass: Button, filename: e:\PoseidonBox\Software\TridentOS\src\gui\kv\styles.kv.
2025-10-05 10:02:42 - src.services.system_monitor - INFO - System monitor initialized
2025-10-05 10:02:42 - src.services.system_monitor - INFO - System monitor started
2025-10-05 10:02:42 - kivy - WARNING - Deprecated property "<BooleanProperty name=allow_stretch>" of object "<kivy.uix.image.Image object at 0x0000016D9B1F88A0>" was accessed, it will be removed in a future version
2025-10-05 10:02:42 - kivy - WARNING - Deprecated property "<BooleanProperty name=keep_ratio>" of object "<kivy.uix.image.Image object at 0x0000016D9B1F88A0>" was accessed, it will be removed in a future version
2025-10-05 10:02:42 - src.services.data_service - INFO - Data service initialized
2025-10-05 10:02:43 - src.services.system_monitor - WARNING - Memory usage high: 93.4%
2025-10-05 10:03:12 - src.services.system_monitor - INFO - System monitor stopped
